import { Button } from "@/components/ui/button";
import HealaLogoIcon from "@/components/icons/heala-logo-icon";
import { CheckCircle } from "lucide-react";
import { Link } from "@tanstack/react-router";

interface DrugPaymentSuccessPageProps {
  paymentId: string;
  reference?: string;
}

export function DrugPaymentSuccessPage({ paymentId, reference }: DrugPaymentSuccessPageProps) {
  return (
    <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 p-4">
      {/* Heala Logo */}
      <div className="mb-6">
        <HealaLogoIcon className="w-16 h-16" />
      </div>

      <div className="bg-white rounded-lg shadow-md w-full max-w-2xl p-8 text-center">
        <div className="flex justify-center mb-6">
          <CheckCircle className="h-16 w-16 text-emerald-500" />
        </div>

        <h1 className="text-2xl font-bold mb-4">Payment Successful!</h1>

        <p className="text-gray-600 mb-6">
          Your payment has been processed successfully. You will receive a confirmation email shortly.
        </p>

        {reference && (
          <div className="bg-gray-50 p-4 rounded-md mb-6">
            <p className="text-sm text-gray-500">Payment Reference</p>
            <p className="font-medium">{reference}</p>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild variant="outline">
            <Link to="/">Return to Home</Link>
          </Button>

          <Button asChild>
            <a href={`mailto:<EMAIL>?subject=Support for Order ${paymentId}`}>
              Contact Support
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}
