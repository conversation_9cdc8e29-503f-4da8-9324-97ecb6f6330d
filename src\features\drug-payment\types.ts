export interface DrugItem {
  _id: string;
  drugName: string;
  drugForm?: string;
  quantity: number;
  drugPrice: number;
}

export interface DeliveryOption {
  id: string;
  name: string;
  description: string;
  price: number;
}

export interface DrugPaymentData {
  id: string;
  patientName: string;
  patientEmail: string;
  drugs: DrugItem[];
  deliveryOptions: DeliveryOption[];
}

export interface DeliveryAddress {
  state: string;
  lga: string;
  city: string;
  street: string;
  landmark: string;
  latitude: number;
  longitude: number;
}


export interface DrugPaymentState {
  paymentData: DrugPaymentData | null;
  selectedDeliveryOption: string | null;
  deliveryAddress: DeliveryAddress | null;
  isLoading: boolean;
  error: string | null;
  setSelectedDeliveryOption: (optionId: string) => void;
  setDeliveryAddress: (address: DeliveryAddress) => void;
  setPaymentData: (data: DrugPaymentData) => void;
  setLoading: (loading: boolean) => void;
  setError: (errorMessage: string | null) => void;
  removeItem: (itemId: string) => void;
}
