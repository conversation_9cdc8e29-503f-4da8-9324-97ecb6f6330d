import { create } from "zustand";
import { DeliveryAddress, DrugPaymentData, DrugPaymentState } from "../types";

// Default delivery options
export const defaultDeliveryOptions = [
  {
    id: "pickup",
    name: "Pick up",
    description: "Pick up your medication physically at your preferred pharmacy.",
    price: 0,
  },
  {
    id: "delivery",
    name: "Delivery",
    description: "Enjoy the convenience of having your drugs delivered to you.",
    price: 3000.00,
  },
];

export const useDrugPaymentStore = create<DrugPaymentState>((set, get) => ({
  paymentData: null,
  selectedDeliveryOption: "pickup", // Default to pickup
  deliveryAddress: null,
  isLoading: false,
  error: null,

  setSelectedDeliveryOption: (optionId: string) => {
    set({ selectedDeliveryOption: optionId });
  },

  setDeliveryAddress: (address: DeliveryAddress) => {
    set({ deliveryAddress: address });
  },

  setPaymentData: (data: DrugPaymentData) => {
    set({
      paymentData: data,
      isLoading: false,
      error: null
    });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (errorMessage: string | null) => {
    set({ error: errorMessage });
  },

  removeItem: (itemId: string) => {
    const { paymentData } = get();
    if (!paymentData) return;

    const updatedDrugs = paymentData.drugs.filter(drug => drug._id !== itemId);

    set({
      paymentData: {
        ...paymentData,
        drugs: updatedDrugs
      }
    });
  }
}));
