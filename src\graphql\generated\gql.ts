/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n    mutation resendOTP($email: String!) {\n        resendOTP(data: { email: $email }) \n    }\n": typeof types.ResendOtpDocument,
    "\n    mutation verifyEmail($email: String!, $otp: String!) {\n        verifyEmail(data: {email: $email, otp: $otp}) {\n            account {\n                _id\n                email\n                dociId\n                access_token\n                refresh_token\n                isEmailVerified\n            }\n        }\n    }\n": typeof types.VerifyEmailDocument,
    "\n  mutation signOutDashboard {\n    logout\n  }\n": typeof types.SignOutDashboardDocument,
    "\n  query getIssues {\n    getIssues {\n      issues {\n        ID\n        Name\n      }\n    }\n  }\n": typeof types.GetIssuesDocument,
    "\n  mutation sendOTP($email: String!) {\n    sendOTP(data: { email: $email })\n  }\n": typeof types.SendOtpDocument,
    "\n    query doctorProfile($profileId: String!) {\n      doctorProfile(data: { id: $profileId }) {\n        profile {\n          _id\n          firstName\n          lastName\n          gender\n          phoneNumber\n          createdAt\n          updatedAt\n          email\n          fee\n          hospital\n          specialization\n          dob\n          cadre\n          picture\n          providerId {\n            _id\n            name\n          }\n          status\n          dociId\n          rating\n          accountDetails {\n            accountName\n            accountNumber\n            bankName\n          }\n        }\n      }\n    }\n  ": typeof types.DoctorProfileDocument,
    "\n  query getUserTypeProviders($hospitalUserTypeId: String, $name: String) {\n    getUserTypeProviders(\n      filterBy: { userTypeId: $hospitalUserTypeId, name: $name }\n      page: -1\n    ) {\n      provider {\n        _id\n        name\n        icon\n        userCount\n        doctorCount\n        enrolleeCount\n        partnerCount\n        createdAt\n        updatedAt\n        userTypeId {\n          name\n          icon\n          createdAt\n          updatedAt\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n": typeof types.GetUserTypeProvidersDocument,
    "\n  query getPartnerBySubdomain($subdomain: String!) {\n    getPartnerBySubdomain(data: { subdomain: $subdomain }) {\n      subdomain\n      providerId\n      widgetColor\n      widgetLogo\n      apiKey\n      category\n    }\n  }\n": typeof types.GetPartnerBySubdomainDocument,
    "\n  query GetRejectedDrugsOrderData($referralId: String!) {\n    getRejectedDrugs(data: {\n      id: $referralId\n    }) {\n      account {\n        access_token\n      }\n      drugs {\n        _id\n        drugForm\n        drugName\n        drugPrice\n        quantity\n      }\n    }\n  }\n": typeof types.GetRejectedDrugsOrderDataDocument,
    "\n  query CheckCharge($data: CheckChargeInput!) {\n    checkCharge(data: $data) {\n      chargeResponse {\n        reference\n        status\n        amount\n        channel\n      }\n    }\n  }\n": typeof types.CheckChargeDocument,
    "\n  query getPharmaciesFromWellaHealth($lga: String, $state: String!) {\n    getPharmaciesFromWellaHealth(data: { state: $state, lga: $lga }) {\n      data {\n        pharmacyCode\n        pharmacyName\n        state\n        lga\n        area\n        address\n      }\n    }\n  }\n": typeof types.GetPharmaciesFromWellaHealthDocument,
    "\n  query PrescriptionReferral($id: String!) {\n    prescriptionReferral(data: {id: $id}) {\n      patient {\n        _id\n        phoneNumber\n      }\n      doctor {\n        _id\n      }\n      consultation {\n        _id\n        pharmacyCode\n        pharmacyName\n        pharmacyAddress\n      }\n      drugsPartner {\n        _id\n      }\n    }\n  }\n": typeof types.PrescriptionReferralDocument,
    "\n  query GetPartnerConfiguration($partnerId: String!) {\n    getPartnerConfiguration(data: {\n      partner: $partnerId\n    }) {\n      drugDeliveryFee\n    }\n  }\n": typeof types.GetPartnerConfigurationDocument,
    "\n  query GetScheduledConsultations(\n    $type: String\n    $first: Int\n    $orderBy: String\n    $page: Int\n    $acceptedStatus: String\n    $pendingStatus: String\n    $patientId: String\n  ) {\n    acceptedConsultations: getConsultations(\n      filterBy: { type: $type, status: $acceptedStatus, patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        fee\n        contactMedium\n        doctor {\n          _id\n          firstName\n          lastName\n          picture\n          specialization\n        }\n        createdAt\n      }\n    }\n\n    pendingConsultations: getConsultations(\n      filterBy: { type: $type, status: $pendingStatus, patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        fee\n        contactMedium\n        doctor {\n          _id\n          firstName\n          lastName\n          picture\n          specialization\n        }\n        createdAt\n      }\n    }\n  }\n": typeof types.GetScheduledConsultationsDocument,
    "\n  query getAllUserConsultations ($first: Int, $orderBy: String, $page: Int, $patientId: String) {\n    getConsultations(\n      filterBy: { patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        createdAt\n        contactMedium\n        doctor{\n          _id\n          firstName\n          lastName\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n": typeof types.GetAllUserConsultationsDocument,
    "\n  query getOverviewStats($patientId: String!) {\n    getConsultations(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n    getReferrals(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n    getPrescriptions(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n  }\n": typeof types.GetOverviewStatsDocument,
    "\n    query getAllPrescriptions($first: Int, $page: Int, $orderBy: String, $patientId: String) {\n    getPrescriptions(\n      filterBy: { patient: $patientId }\n      page: $page\n      first: $first\n      orderBy: $orderBy\n    ) {\n      data {\n        _id\n        doctor{\n          _id\n          firstName\n          lastName\n        }\n        consultation\n        createdAt\n        updatedAt\n        drugs {\n          drugName\n          dosageQuantity\n          dosageUnit\n          route\n          instructions\n          dosageFrequency {\n            timing\n            duration\n          }\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n": typeof types.GetAllPrescriptionsDocument,
    "\n  query getAllTestReferrals($patientId: String, $first: Int, $page: Int, $orderBy: String) {\n  getReferrals(\n    filterBy: {\n      patient: $patientId\n    }\n    orderBy: $orderBy\n    page: $page\n    first: $first\n  ) {\n    referral {\n      _id\n      doctor {\n        _id\n        firstName\n        lastName\n        picture\n        specialization\n        rating\n      }\n      tests {\n        _id\n        name\n        note\n        urgency\n        partner\n        paid\n        createdAt\n        updatedAt\n      }\n      type\n      reason\n      note\n      createdAt\n      updatedAt\n    }\n    pageInfo {\n      totalDocs\n      limit\n      offset\n      hasPrevPage\n      hasNextPage\n      page\n      totalPages\n      pagingCounter\n      prevPage\n      nextPage\n    }\n  }\n}\n  ": typeof types.GetAllTestReferralsDocument,
    "\n  query findProfiles ($healaId: String!) {\n    profiles(search: $healaId) {\n      data {\n        _id\n        firstName\n        lastName\n        height\n        weight\n        bloodGroup\n        genotype\n        gender\n        phoneNumber\n        providerId {\n          _id\n        }\n        plan\n        status\n        consultations\n        createdAt\n        image\n        rating\n        pastIllness {\n          id {\n            _id\n            name\n          }\n        }\n        accountId {\n          _id\n          email\n        }\n      }\n    }\n  }\n": typeof types.FindProfilesDocument,
};
const documents: Documents = {
    "\n    mutation resendOTP($email: String!) {\n        resendOTP(data: { email: $email }) \n    }\n": types.ResendOtpDocument,
    "\n    mutation verifyEmail($email: String!, $otp: String!) {\n        verifyEmail(data: {email: $email, otp: $otp}) {\n            account {\n                _id\n                email\n                dociId\n                access_token\n                refresh_token\n                isEmailVerified\n            }\n        }\n    }\n": types.VerifyEmailDocument,
    "\n  mutation signOutDashboard {\n    logout\n  }\n": types.SignOutDashboardDocument,
    "\n  query getIssues {\n    getIssues {\n      issues {\n        ID\n        Name\n      }\n    }\n  }\n": types.GetIssuesDocument,
    "\n  mutation sendOTP($email: String!) {\n    sendOTP(data: { email: $email })\n  }\n": types.SendOtpDocument,
    "\n    query doctorProfile($profileId: String!) {\n      doctorProfile(data: { id: $profileId }) {\n        profile {\n          _id\n          firstName\n          lastName\n          gender\n          phoneNumber\n          createdAt\n          updatedAt\n          email\n          fee\n          hospital\n          specialization\n          dob\n          cadre\n          picture\n          providerId {\n            _id\n            name\n          }\n          status\n          dociId\n          rating\n          accountDetails {\n            accountName\n            accountNumber\n            bankName\n          }\n        }\n      }\n    }\n  ": types.DoctorProfileDocument,
    "\n  query getUserTypeProviders($hospitalUserTypeId: String, $name: String) {\n    getUserTypeProviders(\n      filterBy: { userTypeId: $hospitalUserTypeId, name: $name }\n      page: -1\n    ) {\n      provider {\n        _id\n        name\n        icon\n        userCount\n        doctorCount\n        enrolleeCount\n        partnerCount\n        createdAt\n        updatedAt\n        userTypeId {\n          name\n          icon\n          createdAt\n          updatedAt\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n": types.GetUserTypeProvidersDocument,
    "\n  query getPartnerBySubdomain($subdomain: String!) {\n    getPartnerBySubdomain(data: { subdomain: $subdomain }) {\n      subdomain\n      providerId\n      widgetColor\n      widgetLogo\n      apiKey\n      category\n    }\n  }\n": types.GetPartnerBySubdomainDocument,
    "\n  query GetRejectedDrugsOrderData($referralId: String!) {\n    getRejectedDrugs(data: {\n      id: $referralId\n    }) {\n      account {\n        access_token\n      }\n      drugs {\n        _id\n        drugForm\n        drugName\n        drugPrice\n        quantity\n      }\n    }\n  }\n": types.GetRejectedDrugsOrderDataDocument,
    "\n  query CheckCharge($data: CheckChargeInput!) {\n    checkCharge(data: $data) {\n      chargeResponse {\n        reference\n        status\n        amount\n        channel\n      }\n    }\n  }\n": types.CheckChargeDocument,
    "\n  query getPharmaciesFromWellaHealth($lga: String, $state: String!) {\n    getPharmaciesFromWellaHealth(data: { state: $state, lga: $lga }) {\n      data {\n        pharmacyCode\n        pharmacyName\n        state\n        lga\n        area\n        address\n      }\n    }\n  }\n": types.GetPharmaciesFromWellaHealthDocument,
    "\n  query PrescriptionReferral($id: String!) {\n    prescriptionReferral(data: {id: $id}) {\n      patient {\n        _id\n        phoneNumber\n      }\n      doctor {\n        _id\n      }\n      consultation {\n        _id\n        pharmacyCode\n        pharmacyName\n        pharmacyAddress\n      }\n      drugsPartner {\n        _id\n      }\n    }\n  }\n": types.PrescriptionReferralDocument,
    "\n  query GetPartnerConfiguration($partnerId: String!) {\n    getPartnerConfiguration(data: {\n      partner: $partnerId\n    }) {\n      drugDeliveryFee\n    }\n  }\n": types.GetPartnerConfigurationDocument,
    "\n  query GetScheduledConsultations(\n    $type: String\n    $first: Int\n    $orderBy: String\n    $page: Int\n    $acceptedStatus: String\n    $pendingStatus: String\n    $patientId: String\n  ) {\n    acceptedConsultations: getConsultations(\n      filterBy: { type: $type, status: $acceptedStatus, patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        fee\n        contactMedium\n        doctor {\n          _id\n          firstName\n          lastName\n          picture\n          specialization\n        }\n        createdAt\n      }\n    }\n\n    pendingConsultations: getConsultations(\n      filterBy: { type: $type, status: $pendingStatus, patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        fee\n        contactMedium\n        doctor {\n          _id\n          firstName\n          lastName\n          picture\n          specialization\n        }\n        createdAt\n      }\n    }\n  }\n": types.GetScheduledConsultationsDocument,
    "\n  query getAllUserConsultations ($first: Int, $orderBy: String, $page: Int, $patientId: String) {\n    getConsultations(\n      filterBy: { patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        createdAt\n        contactMedium\n        doctor{\n          _id\n          firstName\n          lastName\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n": types.GetAllUserConsultationsDocument,
    "\n  query getOverviewStats($patientId: String!) {\n    getConsultations(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n    getReferrals(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n    getPrescriptions(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n  }\n": types.GetOverviewStatsDocument,
    "\n    query getAllPrescriptions($first: Int, $page: Int, $orderBy: String, $patientId: String) {\n    getPrescriptions(\n      filterBy: { patient: $patientId }\n      page: $page\n      first: $first\n      orderBy: $orderBy\n    ) {\n      data {\n        _id\n        doctor{\n          _id\n          firstName\n          lastName\n        }\n        consultation\n        createdAt\n        updatedAt\n        drugs {\n          drugName\n          dosageQuantity\n          dosageUnit\n          route\n          instructions\n          dosageFrequency {\n            timing\n            duration\n          }\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n": types.GetAllPrescriptionsDocument,
    "\n  query getAllTestReferrals($patientId: String, $first: Int, $page: Int, $orderBy: String) {\n  getReferrals(\n    filterBy: {\n      patient: $patientId\n    }\n    orderBy: $orderBy\n    page: $page\n    first: $first\n  ) {\n    referral {\n      _id\n      doctor {\n        _id\n        firstName\n        lastName\n        picture\n        specialization\n        rating\n      }\n      tests {\n        _id\n        name\n        note\n        urgency\n        partner\n        paid\n        createdAt\n        updatedAt\n      }\n      type\n      reason\n      note\n      createdAt\n      updatedAt\n    }\n    pageInfo {\n      totalDocs\n      limit\n      offset\n      hasPrevPage\n      hasNextPage\n      page\n      totalPages\n      pagingCounter\n      prevPage\n      nextPage\n    }\n  }\n}\n  ": types.GetAllTestReferralsDocument,
    "\n  query findProfiles ($healaId: String!) {\n    profiles(search: $healaId) {\n      data {\n        _id\n        firstName\n        lastName\n        height\n        weight\n        bloodGroup\n        genotype\n        gender\n        phoneNumber\n        providerId {\n          _id\n        }\n        plan\n        status\n        consultations\n        createdAt\n        image\n        rating\n        pastIllness {\n          id {\n            _id\n            name\n          }\n        }\n        accountId {\n          _id\n          email\n        }\n      }\n    }\n  }\n": types.FindProfilesDocument,
};

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = gql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function gql(source: string): unknown;

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n    mutation resendOTP($email: String!) {\n        resendOTP(data: { email: $email }) \n    }\n"): (typeof documents)["\n    mutation resendOTP($email: String!) {\n        resendOTP(data: { email: $email }) \n    }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n    mutation verifyEmail($email: String!, $otp: String!) {\n        verifyEmail(data: {email: $email, otp: $otp}) {\n            account {\n                _id\n                email\n                dociId\n                access_token\n                refresh_token\n                isEmailVerified\n            }\n        }\n    }\n"): (typeof documents)["\n    mutation verifyEmail($email: String!, $otp: String!) {\n        verifyEmail(data: {email: $email, otp: $otp}) {\n            account {\n                _id\n                email\n                dociId\n                access_token\n                refresh_token\n                isEmailVerified\n            }\n        }\n    }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation signOutDashboard {\n    logout\n  }\n"): (typeof documents)["\n  mutation signOutDashboard {\n    logout\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query getIssues {\n    getIssues {\n      issues {\n        ID\n        Name\n      }\n    }\n  }\n"): (typeof documents)["\n  query getIssues {\n    getIssues {\n      issues {\n        ID\n        Name\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  mutation sendOTP($email: String!) {\n    sendOTP(data: { email: $email })\n  }\n"): (typeof documents)["\n  mutation sendOTP($email: String!) {\n    sendOTP(data: { email: $email })\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n    query doctorProfile($profileId: String!) {\n      doctorProfile(data: { id: $profileId }) {\n        profile {\n          _id\n          firstName\n          lastName\n          gender\n          phoneNumber\n          createdAt\n          updatedAt\n          email\n          fee\n          hospital\n          specialization\n          dob\n          cadre\n          picture\n          providerId {\n            _id\n            name\n          }\n          status\n          dociId\n          rating\n          accountDetails {\n            accountName\n            accountNumber\n            bankName\n          }\n        }\n      }\n    }\n  "): (typeof documents)["\n    query doctorProfile($profileId: String!) {\n      doctorProfile(data: { id: $profileId }) {\n        profile {\n          _id\n          firstName\n          lastName\n          gender\n          phoneNumber\n          createdAt\n          updatedAt\n          email\n          fee\n          hospital\n          specialization\n          dob\n          cadre\n          picture\n          providerId {\n            _id\n            name\n          }\n          status\n          dociId\n          rating\n          accountDetails {\n            accountName\n            accountNumber\n            bankName\n          }\n        }\n      }\n    }\n  "];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query getUserTypeProviders($hospitalUserTypeId: String, $name: String) {\n    getUserTypeProviders(\n      filterBy: { userTypeId: $hospitalUserTypeId, name: $name }\n      page: -1\n    ) {\n      provider {\n        _id\n        name\n        icon\n        userCount\n        doctorCount\n        enrolleeCount\n        partnerCount\n        createdAt\n        updatedAt\n        userTypeId {\n          name\n          icon\n          createdAt\n          updatedAt\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query getUserTypeProviders($hospitalUserTypeId: String, $name: String) {\n    getUserTypeProviders(\n      filterBy: { userTypeId: $hospitalUserTypeId, name: $name }\n      page: -1\n    ) {\n      provider {\n        _id\n        name\n        icon\n        userCount\n        doctorCount\n        enrolleeCount\n        partnerCount\n        createdAt\n        updatedAt\n        userTypeId {\n          name\n          icon\n          createdAt\n          updatedAt\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query getPartnerBySubdomain($subdomain: String!) {\n    getPartnerBySubdomain(data: { subdomain: $subdomain }) {\n      subdomain\n      providerId\n      widgetColor\n      widgetLogo\n      apiKey\n      category\n    }\n  }\n"): (typeof documents)["\n  query getPartnerBySubdomain($subdomain: String!) {\n    getPartnerBySubdomain(data: { subdomain: $subdomain }) {\n      subdomain\n      providerId\n      widgetColor\n      widgetLogo\n      apiKey\n      category\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query GetRejectedDrugsOrderData($referralId: String!) {\n    getRejectedDrugs(data: {\n      id: $referralId\n    }) {\n      account {\n        access_token\n      }\n      drugs {\n        _id\n        drugForm\n        drugName\n        drugPrice\n        quantity\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetRejectedDrugsOrderData($referralId: String!) {\n    getRejectedDrugs(data: {\n      id: $referralId\n    }) {\n      account {\n        access_token\n      }\n      drugs {\n        _id\n        drugForm\n        drugName\n        drugPrice\n        quantity\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query CheckCharge($data: CheckChargeInput!) {\n    checkCharge(data: $data) {\n      chargeResponse {\n        reference\n        status\n        amount\n        channel\n      }\n    }\n  }\n"): (typeof documents)["\n  query CheckCharge($data: CheckChargeInput!) {\n    checkCharge(data: $data) {\n      chargeResponse {\n        reference\n        status\n        amount\n        channel\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query getPharmaciesFromWellaHealth($lga: String, $state: String!) {\n    getPharmaciesFromWellaHealth(data: { state: $state, lga: $lga }) {\n      data {\n        pharmacyCode\n        pharmacyName\n        state\n        lga\n        area\n        address\n      }\n    }\n  }\n"): (typeof documents)["\n  query getPharmaciesFromWellaHealth($lga: String, $state: String!) {\n    getPharmaciesFromWellaHealth(data: { state: $state, lga: $lga }) {\n      data {\n        pharmacyCode\n        pharmacyName\n        state\n        lga\n        area\n        address\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query PrescriptionReferral($id: String!) {\n    prescriptionReferral(data: {id: $id}) {\n      patient {\n        _id\n        phoneNumber\n      }\n      doctor {\n        _id\n      }\n      consultation {\n        _id\n        pharmacyCode\n        pharmacyName\n        pharmacyAddress\n      }\n      drugsPartner {\n        _id\n      }\n    }\n  }\n"): (typeof documents)["\n  query PrescriptionReferral($id: String!) {\n    prescriptionReferral(data: {id: $id}) {\n      patient {\n        _id\n        phoneNumber\n      }\n      doctor {\n        _id\n      }\n      consultation {\n        _id\n        pharmacyCode\n        pharmacyName\n        pharmacyAddress\n      }\n      drugsPartner {\n        _id\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query GetPartnerConfiguration($partnerId: String!) {\n    getPartnerConfiguration(data: {\n      partner: $partnerId\n    }) {\n      drugDeliveryFee\n    }\n  }\n"): (typeof documents)["\n  query GetPartnerConfiguration($partnerId: String!) {\n    getPartnerConfiguration(data: {\n      partner: $partnerId\n    }) {\n      drugDeliveryFee\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query GetScheduledConsultations(\n    $type: String\n    $first: Int\n    $orderBy: String\n    $page: Int\n    $acceptedStatus: String\n    $pendingStatus: String\n    $patientId: String\n  ) {\n    acceptedConsultations: getConsultations(\n      filterBy: { type: $type, status: $acceptedStatus, patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        fee\n        contactMedium\n        doctor {\n          _id\n          firstName\n          lastName\n          picture\n          specialization\n        }\n        createdAt\n      }\n    }\n\n    pendingConsultations: getConsultations(\n      filterBy: { type: $type, status: $pendingStatus, patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        fee\n        contactMedium\n        doctor {\n          _id\n          firstName\n          lastName\n          picture\n          specialization\n        }\n        createdAt\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetScheduledConsultations(\n    $type: String\n    $first: Int\n    $orderBy: String\n    $page: Int\n    $acceptedStatus: String\n    $pendingStatus: String\n    $patientId: String\n  ) {\n    acceptedConsultations: getConsultations(\n      filterBy: { type: $type, status: $acceptedStatus, patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        fee\n        contactMedium\n        doctor {\n          _id\n          firstName\n          lastName\n          picture\n          specialization\n        }\n        createdAt\n      }\n    }\n\n    pendingConsultations: getConsultations(\n      filterBy: { type: $type, status: $pendingStatus, patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        fee\n        contactMedium\n        doctor {\n          _id\n          firstName\n          lastName\n          picture\n          specialization\n        }\n        createdAt\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query getAllUserConsultations ($first: Int, $orderBy: String, $page: Int, $patientId: String) {\n    getConsultations(\n      filterBy: { patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        createdAt\n        contactMedium\n        doctor{\n          _id\n          firstName\n          lastName\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query getAllUserConsultations ($first: Int, $orderBy: String, $page: Int, $patientId: String) {\n    getConsultations(\n      filterBy: { patient: $patientId }\n      first: $first\n      orderBy: $orderBy\n      page: $page\n    ) {\n      data {\n        _id\n        status\n        type\n        time\n        createdAt\n        contactMedium\n        doctor{\n          _id\n          firstName\n          lastName\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query getOverviewStats($patientId: String!) {\n    getConsultations(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n    getReferrals(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n    getPrescriptions(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n  }\n"): (typeof documents)["\n  query getOverviewStats($patientId: String!) {\n    getConsultations(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n    getReferrals(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n    getPrescriptions(filterBy: { patient: $patientId }) {\n      pageInfo {\n        totalDocs\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n    query getAllPrescriptions($first: Int, $page: Int, $orderBy: String, $patientId: String) {\n    getPrescriptions(\n      filterBy: { patient: $patientId }\n      page: $page\n      first: $first\n      orderBy: $orderBy\n    ) {\n      data {\n        _id\n        doctor{\n          _id\n          firstName\n          lastName\n        }\n        consultation\n        createdAt\n        updatedAt\n        drugs {\n          drugName\n          dosageQuantity\n          dosageUnit\n          route\n          instructions\n          dosageFrequency {\n            timing\n            duration\n          }\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n"): (typeof documents)["\n    query getAllPrescriptions($first: Int, $page: Int, $orderBy: String, $patientId: String) {\n    getPrescriptions(\n      filterBy: { patient: $patientId }\n      page: $page\n      first: $first\n      orderBy: $orderBy\n    ) {\n      data {\n        _id\n        doctor{\n          _id\n          firstName\n          lastName\n        }\n        consultation\n        createdAt\n        updatedAt\n        drugs {\n          drugName\n          dosageQuantity\n          dosageUnit\n          route\n          instructions\n          dosageFrequency {\n            timing\n            duration\n          }\n        }\n      }\n      pageInfo {\n        totalDocs\n        limit\n        offset\n        hasPrevPage\n        hasNextPage\n        page\n        totalPages\n        pagingCounter\n        prevPage\n        nextPage\n      }\n    }\n  }\n"];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query getAllTestReferrals($patientId: String, $first: Int, $page: Int, $orderBy: String) {\n  getReferrals(\n    filterBy: {\n      patient: $patientId\n    }\n    orderBy: $orderBy\n    page: $page\n    first: $first\n  ) {\n    referral {\n      _id\n      doctor {\n        _id\n        firstName\n        lastName\n        picture\n        specialization\n        rating\n      }\n      tests {\n        _id\n        name\n        note\n        urgency\n        partner\n        paid\n        createdAt\n        updatedAt\n      }\n      type\n      reason\n      note\n      createdAt\n      updatedAt\n    }\n    pageInfo {\n      totalDocs\n      limit\n      offset\n      hasPrevPage\n      hasNextPage\n      page\n      totalPages\n      pagingCounter\n      prevPage\n      nextPage\n    }\n  }\n}\n  "): (typeof documents)["\n  query getAllTestReferrals($patientId: String, $first: Int, $page: Int, $orderBy: String) {\n  getReferrals(\n    filterBy: {\n      patient: $patientId\n    }\n    orderBy: $orderBy\n    page: $page\n    first: $first\n  ) {\n    referral {\n      _id\n      doctor {\n        _id\n        firstName\n        lastName\n        picture\n        specialization\n        rating\n      }\n      tests {\n        _id\n        name\n        note\n        urgency\n        partner\n        paid\n        createdAt\n        updatedAt\n      }\n      type\n      reason\n      note\n      createdAt\n      updatedAt\n    }\n    pageInfo {\n      totalDocs\n      limit\n      offset\n      hasPrevPage\n      hasNextPage\n      page\n      totalPages\n      pagingCounter\n      prevPage\n      nextPage\n    }\n  }\n}\n  "];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n  query findProfiles ($healaId: String!) {\n    profiles(search: $healaId) {\n      data {\n        _id\n        firstName\n        lastName\n        height\n        weight\n        bloodGroup\n        genotype\n        gender\n        phoneNumber\n        providerId {\n          _id\n        }\n        plan\n        status\n        consultations\n        createdAt\n        image\n        rating\n        pastIllness {\n          id {\n            _id\n            name\n          }\n        }\n        accountId {\n          _id\n          email\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query findProfiles ($healaId: String!) {\n    profiles(search: $healaId) {\n      data {\n        _id\n        firstName\n        lastName\n        height\n        weight\n        bloodGroup\n        genotype\n        gender\n        phoneNumber\n        providerId {\n          _id\n        }\n        plan\n        status\n        consultations\n        createdAt\n        image\n        rating\n        pastIllness {\n          id {\n            _id\n            name\n          }\n        }\n        accountId {\n          _id\n          email\n        }\n      }\n    }\n  }\n"];

export function gql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;