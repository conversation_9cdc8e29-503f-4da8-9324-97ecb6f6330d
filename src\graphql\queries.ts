import { gql } from "@/graphql/generated/gql";

export const getPartnerBySubdomainQuery = gql(`
  query getPartnerBySubdomain($subdomain: String!) {
    getPartnerBySubdomain(data: { subdomain: $subdomain }) {
      subdomain
      providerId
      widgetColor
      widgetLogo
      apiKey
      category
    }
  }
`);

export const getRejectedDrugsQuery = gql(`
  query GetRejectedDrugsOrderData($referralId: String!) {
    getRejectedDrugs(data: {
      id: $referralId
    }) {
      account {
        access_token
      }
      drugs {
        _id
        drugForm
        drugName
        drugPrice
        quantity
      }
    }
  }
`);

export const checkChargeQuery = gql(`
  query CheckCharge($data: CheckChargeInput!) {
    checkCharge(data: $data) {
      chargeResponse {
        id
        status
        reference
        amount
        message
        gateway_response
        paid_at
        channel
        currency
        authorization {
          authorization_code
          bin
          last4
          exp_month
          exp_year
          channel
          card_type
          bank
          country_code
          brand
          reusable
          signature
          account_name
        }
      }
    }
  }
`)

export const getWellaHealthPharmaciesQuery = gql(`
  query getPharmaciesFromWellaHealth($lga: String, $state: String!) {
    getPharmaciesFromWellaHealth(data: { state: $state, lga: $lga }) {
      data {
        pharmacyCode
        pharmacyName
        state
        lga
        area
        address
      }
    }
  }
`);

export const prescriptionReferralQuery = gql(`
  query PrescriptionReferral($id: String!) {
    prescriptionReferral(data: {id: $id}) {
      patient {
        _id
        phoneNumber
        email
      }
      doctor {
        _id
      }
      consultation {
        _id
        pharmacyCode
        pharmacyName
        pharmacyAddress
      }
      drugsPartner {
        _id
      }
    }
  }
`);

export const getPartnerConfigurationQuery = gql(`
  query GetPartnerConfiguration($partnerId: String!) {
    getPartnerConfiguration(data: {
      partner: $partnerId
    }) {
      drugDeliveryFee
    }
  }
`);

export const enrolleeDrugOrderMutation = gql(`
  mutation EnrolleeDrugOrder($data: EnrolleeOrderInput!) {
    enrolleeDrugOrder(data: $data) {
      drugOrder {
        _id
      }
    }
  }
`);

export const initPaymentMutation = gql(`
  mutation InitPayment($data: PaymentInitInput!) {
    initPayment(data: $data) {
      paymentInitResponse {
        authorization_url
        reference
      }
      errors {
        field
        message
      }
    }
  }
`);