import { gql } from "@/graphql/generated/gql";

export const getPartnerBySubdomainQuery = gql(`
  query getPartnerBySubdomain($subdomain: String!) {
    getPartnerBySubdomain(data: { subdomain: $subdomain }) {
      subdomain
      providerId
      widgetColor
      widgetLogo
      apiKey
      category
    }
  }
`);

export const getRejectedDrugsQuery = gql(`
  query GetRejectedDrugsOrderData($referralId: String!) {
    getRejectedDrugs(data: {
      id: $referralId
    }) {
      account {
        access_token
      }
      drugs {
        _id
        drugForm
        drugName
        drugPrice
        quantity
      }
    }
  }
`);

export const checkChargeQuery = gql(`
  query CheckCharge($data: CheckChargeInput!) {
    checkCharge(data: $data) {
      chargeResponse {
        reference
        status
        amount
        channel
      }
    }
  }
`)

export const getWellaHealthPharmaciesQuery = gql(`
  query getPharmaciesFromWellaHealth($lga: String, $state: String!) {
    getPharmaciesFromWellaHealth(data: { state: $state, lga: $lga }) {
      data {
        pharmacyCode
        pharmacyName
        state
        lga
        area
        address
      }
    }
  }
`);