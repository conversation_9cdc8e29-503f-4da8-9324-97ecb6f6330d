import { Head } from '@/components/head'
import { DrugPaymentSuccessPage } from '@/features/drug-payment/components/drug-payment-success-page'
import { createFileRoute } from '@tanstack/react-router'
import { zodValidator } from '@tanstack/zod-adapter'
import { z } from 'zod'

export const Route = createFileRoute('/drug-payment-success/$referralId')({
  component: RouteComponent,
  validateSearch: zodValidator(
    z.object({
      reference: z.string().optional(),
    }),
  ),
})

function RouteComponent() {
  const { paymentId } = Route.useParams()
  const { reference } = Route.useSearch()

  return (
    <>
      <Head title="Payment Successful" />
      <DrugPaymentSuccessPage paymentId={paymentId} reference={reference} />
    </>
  )
}
