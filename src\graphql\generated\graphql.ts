/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format. */
  DateTime: { input: any; output: any; }
  /** Date custom scalar type */
  DateTimeScalar: { input: any; output: any; }
  /** Email custom scalar type */
  EmailAddress: { input: any; output: any; }
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: { input: any; output: any; }
  /** JSONObject custom scalar type */
  JSONObject: { input: any; output: any; }
  /** UnsignedInt custom scalar type */
  UnsignedInt: { input: any; output: any; }
};

export type AcceptConsultationInput = {
  doctor: Scalars['ID']['input'];
  id: Scalars['ID']['input'];
};

export type Account = {
  __typename?: 'Account';
  _id?: Maybe<Scalars['ID']['output']>;
  access_token?: Maybe<Scalars['String']['output']>;
  apiKey?: Maybe<Scalars['String']['output']>;
  authType?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deactivateType?: Maybe<Scalars['String']['output']>;
  deactivated?: Maybe<Scalars['Boolean']['output']>;
  deactivatedAt?: Maybe<Scalars['String']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeNumber?: Maybe<Scalars['String']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  isEmailVerified?: Maybe<Scalars['Boolean']['output']>;
  isPasswordTemporary?: Maybe<Scalars['Boolean']['output']>;
  lastLogin?: Maybe<Scalars['DateTime']['output']>;
  nextStep?: Maybe<Scalars['String']['output']>;
  otp?: Maybe<Scalars['String']['output']>;
  otpExpiresAt?: Maybe<Scalars['DateTime']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  refresh_token?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Role>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userTypeId?: Maybe<UserType>;
};

export type AccountConnection = {
  __typename?: 'AccountConnection';
  data: Array<PopulatedAccount>;
  pageInfo?: Maybe<PageInfo>;
};

export type AccountData = {
  __typename?: 'AccountData';
  _id?: Maybe<Scalars['ID']['output']>;
  apiKey?: Maybe<Scalars['String']['output']>;
  authType?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deactivateType?: Maybe<Scalars['String']['output']>;
  deactivated?: Maybe<Scalars['Boolean']['output']>;
  deactivatedAt?: Maybe<Scalars['String']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeNumber?: Maybe<Scalars['String']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  isEmailVerified?: Maybe<Scalars['Boolean']['output']>;
  isPasswordTemporary?: Maybe<Scalars['Boolean']['output']>;
  nextStep?: Maybe<Scalars['String']['output']>;
  otp?: Maybe<Scalars['String']['output']>;
  otpExpiresAt?: Maybe<Scalars['DateTime']['output']>;
  providerId?: Maybe<Scalars['ID']['output']>;
  role?: Maybe<Role>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userTypeId?: Maybe<Scalars['ID']['output']>;
};

export type AccountDetail = {
  __typename?: 'AccountDetail';
  accountName?: Maybe<Scalars['String']['output']>;
  accountNumber?: Maybe<Scalars['String']['output']>;
  bankName?: Maybe<Scalars['String']['output']>;
  nuban?: Maybe<Scalars['String']['output']>;
  recipientCode?: Maybe<Scalars['String']['output']>;
};

export type AccountDetails = {
  __typename?: 'AccountDetails';
  accountName?: Maybe<Scalars['String']['output']>;
  accountNumber?: Maybe<Scalars['String']['output']>;
  bankName?: Maybe<Scalars['String']['output']>;
  nuban?: Maybe<Scalars['String']['output']>;
};

export type AccountPayload = {
  __typename?: 'AccountPayload';
  account?: Maybe<Account>;
  errors?: Maybe<ErrorPayload>;
  message?: Maybe<Scalars['String']['output']>;
};

export type AccountProfile = {
  __typename?: 'AccountProfile';
  account?: Maybe<AccountData>;
  profile?: Maybe<CreateProfile>;
};

export type ActivityLog = {
  __typename?: 'ActivityLog';
  _id?: Maybe<Scalars['ID']['output']>;
  arguments?: Maybe<Scalars['JSON']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  endpoint?: Maybe<Scalars['String']['output']>;
  ipAddress?: Maybe<Scalars['String']['output']>;
  operation?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['JSON']['output']>;
  timestamp?: Maybe<Scalars['DateTime']['output']>;
  userAgent?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['ID']['output']>;
};

export type AddBankAccountInput = {
  accountName?: InputMaybe<Scalars['String']['input']>;
  accountNumber?: InputMaybe<Scalars['String']['input']>;
  bankName?: InputMaybe<Scalars['String']['input']>;
  doctorId?: InputMaybe<Scalars['String']['input']>;
  nuban?: InputMaybe<Scalars['String']['input']>;
};

export type AddDependantInput = {
  dob: Scalars['String']['input'];
  email: Scalars['String']['input'];
  firstName?: InputMaybe<Scalars['String']['input']>;
  gender: Scalars['String']['input'];
  image?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  phoneNumber: Scalars['String']['input'];
  relationship: Scalars['String']['input'];
};

export type AddDependants = {
  dependants?: InputMaybe<Array<AddDependantInput>>;
  employeeId: Scalars['String']['input'];
};

export type AddDiagnosticReferralInput = {
  consultationId?: InputMaybe<Scalars['String']['input']>;
  doctor: Scalars['String']['input'];
  labInfo?: InputMaybe<LabInfoInput>;
  note?: InputMaybe<Scalars['String']['input']>;
  patient: Scalars['String']['input'];
  provisionalDiagnosis?: InputMaybe<Scalars['String']['input']>;
  reason: Scalars['String']['input'];
  referralId: Scalars['String']['input'];
  sampleCollection: Scalars['String']['input'];
  total?: InputMaybe<Scalars['Float']['input']>;
  userLocation?: InputMaybe<UserLocationInput>;
};

export type AddDiagnosticTestInput = {
  id: Scalars['String']['input'];
  time: Scalars['String']['input'];
};

export type AddDoctorConsultationInput = {
  consultationId: Scalars['String']['input'];
  contactMedium?: InputMaybe<Scalars['String']['input']>;
  diagnosis?: InputMaybe<Array<Diagnosis>>;
  doctor: Scalars['String']['input'];
  doctorNote?: InputMaybe<Scalars['String']['input']>;
  joined?: InputMaybe<Scalars['Boolean']['input']>;
  referralId?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type AddDrugOrderInput = {
  consultationId: Scalars['String']['input'];
  deliveryOption: Scalars['String']['input'];
  doctor: Scalars['String']['input'];
  note?: InputMaybe<Scalars['String']['input']>;
  patient: Scalars['String']['input'];
  pharmacyAddress?: InputMaybe<Scalars['String']['input']>;
  pharmacyCode?: InputMaybe<Scalars['String']['input']>;
  pharmacyName?: InputMaybe<Scalars['String']['input']>;
  prescriptionDate?: InputMaybe<Scalars['String']['input']>;
  prescriptions?: InputMaybe<Array<Scalars['JSON']['input']>>;
  userLocation?: InputMaybe<UserLocationInput>;
};

export type AddEmployeeInput = {
  businessId: Scalars['String']['input'];
  dependants?: InputMaybe<Array<AddDependantInput>>;
  dob: Scalars['String']['input'];
  email: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  gender: Scalars['String']['input'];
  image?: InputMaybe<Scalars['String']['input']>;
  lastName: Scalars['String']['input'];
  noofdependants?: InputMaybe<Scalars['Float']['input']>;
  phoneNumber: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type AddHospitalMedicationInput = {
  consultationId?: InputMaybe<Scalars['String']['input']>;
  deliveryOption?: InputMaybe<Scalars['String']['input']>;
  doctor: Scalars['String']['input'];
  drugs: Array<DrugsInput>;
  note?: InputMaybe<Scalars['String']['input']>;
  partner: Scalars['String']['input'];
  patient: Scalars['String']['input'];
  pharmacyCode?: InputMaybe<Scalars['String']['input']>;
  prescriptionDate?: InputMaybe<Scalars['String']['input']>;
  userLocation?: InputMaybe<UserLocationInput>;
};

export type AddPartnerInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  bankDetails?: InputMaybe<Array<BankDetailsInput>>;
  category: Scalars['String']['input'];
  classification?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  logoImageUrl: Scalars['String']['input'];
  name: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
  providerId: Scalars['String']['input'];
  specialisation?: InputMaybe<Scalars['String']['input']>;
};

export type AddPastIllnessInput = {
  id: Scalars['ID']['input'];
};

export type AddPrescription = {
  consultationId: Scalars['String']['input'];
};

export type AddPrescriptionReferralInput = {
  consultationId: Scalars['String']['input'];
  drugs?: InputMaybe<Array<DrugInput>>;
  drugsNote?: InputMaybe<Scalars['String']['input']>;
  specialist?: InputMaybe<Array<SpecialistInput>>;
  tests?: InputMaybe<Array<TestInput>>;
  testsNote?: InputMaybe<Scalars['String']['input']>;
};

export type AddPrescriptionsInput = {
  consultationId: Scalars['String']['input'];
  prescriptions: Array<PrescriptionInput>;
};

export type AddQuoteInput = {
  comment?: InputMaybe<Scalars['String']['input']>;
  companyName: Scalars['String']['input'];
  email: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  noOfEmployees: Scalars['Float']['input'];
  phoneNumber: Scalars['String']['input'];
  state: Scalars['String']['input'];
};

export type AddRatingInput = {
  comment?: InputMaybe<Scalars['String']['input']>;
  consultationId: Scalars['String']['input'];
  review?: InputMaybe<Scalars['String']['input']>;
  score: Scalars['Float']['input'];
};

export type AddSpecializationInput = {
  name?: InputMaybe<Scalars['String']['input']>;
};

export type AllProviderConnection = {
  __typename?: 'AllProviderConnection';
  pageInfo?: Maybe<PageInfo>;
  provider?: Maybe<Array<ProvidersType>>;
};

export type AllStats = {
  __typename?: 'AllStats';
  availabilityCalender?: Maybe<AvailabilityCalender>;
  consultationStats?: Maybe<Stat>;
  doctorStats?: Maybe<Stat>;
  drugEarningStats?: Maybe<Stat>;
  earningStats?: Maybe<Stat>;
  enrolleeStats?: Maybe<Stat>;
  partnerStats?: Maybe<Stat>;
  patientStats?: Maybe<Stat>;
  payoutStats?: Maybe<Stat>;
  subscriptionEarningStats?: Maybe<Stat>;
  subscriptionStats?: Maybe<Stat>;
  testEarningStats?: Maybe<Stat>;
};

export type Allergy = {
  __typename?: 'Allergy';
  _id?: Maybe<Scalars['ID']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  food?: Maybe<Scalars['String']['output']>;
  medication?: Maybe<Scalars['String']['output']>;
  profile?: Maybe<Profile>;
  severity?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type AllergyArrayPayload = {
  __typename?: 'AllergyArrayPayload';
  allergies: Array<Allergy>;
  pageInfo: PageInfo;
};

export type AllergyInput = {
  id: Scalars['String']['input'];
};

export type AllergyPayload = {
  __typename?: 'AllergyPayload';
  allergy: Allergy;
};

export type AllowedFeatures = {
  __typename?: 'AllowedFeatures';
  consultation?: Maybe<Scalars['String']['output']>;
};

export type AlumniAssociation = {
  facebook_group_name: Scalars['String']['input'];
  instagram_handle: Scalars['String']['input'];
};

export type ApikeyPayload = {
  __typename?: 'ApikeyPayload';
  account: AccountData;
  message: Scalars['String']['output'];
};

export type Appointment = {
  __typename?: 'Appointment';
  _id: Scalars['ID']['output'];
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  date?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  doctor?: Maybe<DoctorProfile>;
  patient?: Maybe<Profile>;
  providerId?: Maybe<Provider>;
  time?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type AppointmentConnection = {
  __typename?: 'AppointmentConnection';
  data: Array<Appointment>;
  pageInfo?: Maybe<PageInfo>;
};

export type AppointmentInput = {
  id: Scalars['String']['input'];
};

export type AppointmentPayload = {
  __typename?: 'AppointmentPayload';
  appointment?: Maybe<Appointment>;
  errors?: Maybe<Array<ErrorPayload>>;
};

export type AppointmentStats = {
  __typename?: 'AppointmentStats';
  totalPast?: Maybe<Scalars['Float']['output']>;
  totalUpcoming?: Maybe<Scalars['Float']['output']>;
};

export type Availability = {
  __typename?: 'Availability';
  _id?: Maybe<Scalars['ID']['output']>;
  available?: Maybe<Scalars['Boolean']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  day?: Maybe<Scalars['String']['output']>;
  doctor?: Maybe<DoctorProfile>;
  providerId?: Maybe<Scalars['String']['output']>;
  times?: Maybe<Array<AvailabilityTypeArray>>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type AvailabilityArray = {
  start: Scalars['String']['input'];
  stop: Scalars['String']['input'];
};

export type AvailabilityCalender = {
  __typename?: 'AvailabilityCalender';
  availableDoctors?: Maybe<Array<DoctorAvailabilityStat>>;
  today?: Maybe<Scalars['String']['output']>;
};

export type AvailabilityConnection = {
  __typename?: 'AvailabilityConnection';
  availability?: Maybe<Array<Availability>>;
  bookedTimes?: Maybe<Array<BookedConsultationTime>>;
  errors?: Maybe<Array<ErrorPayload>>;
  pageInfo?: Maybe<PageInfo>;
};

export type AvailabilityPayload = {
  __typename?: 'AvailabilityPayload';
  availability?: Maybe<Availability>;
  errors?: Maybe<Array<ErrorPayload>>;
};

export type AvailabilityTypeArray = {
  __typename?: 'AvailabilityTypeArray';
  _id?: Maybe<Scalars['String']['output']>;
  available?: Maybe<Scalars['Boolean']['output']>;
  passed?: Maybe<Scalars['Boolean']['output']>;
  start?: Maybe<Scalars['String']['output']>;
  stop?: Maybe<Scalars['String']['output']>;
};

export type AvailableDoctorTimesInput = {
  date?: InputMaybe<Scalars['String']['input']>;
  day?: InputMaybe<Scalars['String']['input']>;
  doctor: Scalars['String']['input'];
};

export type AvailableIdInput = {
  id: Scalars['String']['input'];
};

export type AvailableTime = {
  __typename?: 'AvailableTime';
  _id: Scalars['ID']['output'];
  available: Scalars['Boolean']['output'];
  createdAt: Scalars['DateTime']['output'];
  day: Scalars['String']['output'];
  providerId: Scalars['String']['output'];
  times: Array<AvailabilityTypeArray>;
  updatedAt: Scalars['DateTime']['output'];
};

export type AvailableTimeConnection = {
  __typename?: 'AvailableTimeConnection';
  data: Array<AvailableTime>;
  pageInfo?: Maybe<PageInfo>;
};

export type AvailableTimeIdInput = {
  id: Scalars['String']['input'];
};

export type AvailableTimeInput = {
  day: Scalars['String']['input'];
  providerId: Scalars['String']['input'];
  times: Array<AvailabilityArray>;
};

export type AvailableTimePayload = {
  __typename?: 'AvailableTimePayload';
  availableTimes?: Maybe<AvailableTime>;
  errors?: Maybe<Array<ErrorPayload>>;
};

export type AvailableTimeStats = {
  __typename?: 'AvailableTimeStats';
  _id?: Maybe<Scalars['ID']['output']>;
  available?: Maybe<Scalars['Boolean']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  day: Scalars['String']['output'];
  doctor?: Maybe<DoctorProfile>;
  providerId?: Maybe<Scalars['String']['output']>;
  times?: Maybe<Array<AvailabilityTypeArray>>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type BankDetails = {
  __typename?: 'BankDetails';
  accName?: Maybe<Scalars['String']['output']>;
  accNumber?: Maybe<Scalars['String']['output']>;
  default?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  nuban?: Maybe<Scalars['String']['output']>;
};

export type BankDetailsInput = {
  accName?: InputMaybe<Scalars['String']['input']>;
  accNumber?: InputMaybe<Scalars['String']['input']>;
  default?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  nuban?: InputMaybe<Scalars['String']['input']>;
};

export type Benefit = {
  __typename?: 'Benefit';
  code?: Maybe<Scalars['Float']['output']>;
  dateAdded?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  level?: Maybe<Scalars['Float']['output']>;
  limit?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type BenefitInput = {
  code?: InputMaybe<Scalars['Float']['input']>;
  dateAdded?: InputMaybe<Scalars['DateTime']['input']>;
  description: Scalars['String']['input'];
  level?: InputMaybe<Scalars['Float']['input']>;
  limit?: InputMaybe<Scalars['Float']['input']>;
  name: Scalars['String']['input'];
};

export type BenefitPayload = {
  __typename?: 'BenefitPayload';
  code?: Maybe<Scalars['Float']['output']>;
  dateAdded?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  level?: Maybe<Scalars['Float']['output']>;
  limit?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type BookedConsultationTime = {
  __typename?: 'BookedConsultationTime';
  date?: Maybe<Scalars['String']['output']>;
  day?: Maybe<Scalars['String']['output']>;
  hour?: Maybe<Scalars['Int']['output']>;
  minute?: Maybe<Scalars['Int']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type BookedConsultationTimeConnection = {
  __typename?: 'BookedConsultationTimeConnection';
  data: Array<BookedConsultationTime>;
};

export type BookedConsultationTimeInput = {
  date?: InputMaybe<Scalars['String']['input']>;
  day?: InputMaybe<Scalars['String']['input']>;
  doctor: Scalars['String']['input'];
};

export type BulkUpdateInput = {
  ids?: InputMaybe<Array<Scalars['String']['input']>>;
  referralId: Scalars['String']['input'];
  status: Scalars['String']['input'];
};

export type Business = {
  __typename?: 'Business';
  address?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  noOfEmployees?: Maybe<Scalars['Float']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userTypeId?: Maybe<Scalars['String']['output']>;
};

export type CancelConsultationInput = {
  id: Scalars['String']['input'];
  reason?: InputMaybe<Scalars['String']['input']>;
};

export type CancelDiagnosticTestInput = {
  id: Scalars['String']['input'];
  reason: Scalars['String']['input'];
};

export type CancelDisputeInput = {
  disputeResolvedReason: Scalars['String']['input'];
  id: Scalars['String']['input'];
};

export type CancelDrugOrderInput = {
  id: Scalars['String']['input'];
  reason: Scalars['String']['input'];
};

export type CancelSubscriptionConnection = {
  __typename?: 'CancelSubscriptionConnection';
  cancelSubscription: Scalars['Boolean']['output'];
};

export type Card = {
  __typename?: 'Card';
  _id: Scalars['String']['output'];
  authorization: Scalars['String']['output'];
  bin: Scalars['String']['output'];
  brand: Scalars['String']['output'];
  createdAt: Scalars['DateTimeScalar']['output'];
  expMonth: Scalars['String']['output'];
  expYear: Scalars['String']['output'];
  last4: Scalars['String']['output'];
  reusable: Scalars['String']['output'];
  signature: Scalars['String']['output'];
  updatedAt: Scalars['DateTimeScalar']['output'];
};

export type CardInput = {
  user: Scalars['String']['input'];
};

export type CardsConnections = {
  __typename?: 'CardsConnections';
  data: Array<Card>;
};

export type ChangeApprovalStatus = {
  id: Scalars['String']['input'];
  status: Scalars['String']['input'];
};

export type ChatConversation = {
  __typename?: 'ChatConversation';
  _id?: Maybe<Scalars['String']['output']>;
  lastMessage?: Maybe<ChatMessage>;
  partner?: Maybe<Scalars['JSON']['output']>;
  user?: Maybe<Scalars['JSON']['output']>;
};

export type ChatConversationsConnection = {
  __typename?: 'ChatConversationsConnection';
  data: Array<ChatConversation>;
};

export type ChatMessage = {
  __typename?: 'ChatMessage';
  _id?: Maybe<Scalars['String']['output']>;
  consultationId?: Maybe<Scalars['String']['output']>;
  content?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  read?: Maybe<Scalars['Boolean']['output']>;
  received?: Maybe<Scalars['Boolean']['output']>;
  receiver?: Maybe<Scalars['JSON']['output']>;
  receiverData?: Maybe<Scalars['JSON']['output']>;
  receiverRole?: Maybe<Scalars['String']['output']>;
  sender?: Maybe<Scalars['JSON']['output']>;
  senderData?: Maybe<Scalars['JSON']['output']>;
  senderRole?: Maybe<Scalars['String']['output']>;
  sent?: Maybe<Scalars['Boolean']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type ChatMessageInput = {
  consultationId: Scalars['String']['input'];
  content: Scalars['String']['input'];
  receiver: Scalars['String']['input'];
  receiverRole: Scalars['String']['input'];
  sender: Scalars['String']['input'];
  senderRole: Scalars['String']['input'];
};

export type ChatMessagePayload = {
  __typename?: 'ChatMessagePayload';
  errors?: Maybe<Array<ErrorPayload>>;
  message?: Maybe<ChatMessage>;
};

export type ChatMessagesPayload = {
  __typename?: 'ChatMessagesPayload';
  data?: Maybe<Array<ChatMessage>>;
  errors?: Maybe<Array<ErrorPayload>>;
};

export type ChatMessagesUploadInput = {
  consultationId: Scalars['String']['input'];
  messages?: InputMaybe<Array<UploadChatMessageInput>>;
};

export type CheckChargeConnection = {
  __typename?: 'CheckChargeConnection';
  chargeResponse: CheckChargeResponse;
};

export type CheckChargeInput = {
  reference: Scalars['String']['input'];
};

export type CheckChargeResponse = {
  __typename?: 'CheckChargeResponse';
  amount?: Maybe<Scalars['String']['output']>;
  authorization?: Maybe<PaystackAuthorization>;
  channel?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  gateway_response?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  paid_at?: Maybe<Scalars['String']['output']>;
  reference?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type CheckHasSubscriptionConnection = {
  __typename?: 'CheckHasSubscriptionConnection';
  hasSubscription: Scalars['Boolean']['output'];
};

export type Company = {
  __typename?: 'Company';
  _id?: Maybe<Scalars['ID']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeCount?: Maybe<Scalars['Int']['output']>;
  logo?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  providerId: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type CompanyConnection = {
  __typename?: 'CompanyConnection';
  data?: Maybe<Array<Company>>;
  pageInfo?: Maybe<PageInfo>;
};

export type CompanyInput = {
  id: Scalars['String']['input'];
};

export type CompanyPayload = {
  __typename?: 'CompanyPayload';
  company?: Maybe<Company>;
  message?: Maybe<Scalars['String']['output']>;
};

export type CompleteDiagnosticTestInput = {
  id: Scalars['String']['input'];
  testResults: Array<TestResultsInput>;
};

export type CompletePasswordInput = {
  email: Scalars['String']['input'];
  otp: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type ConfirmEmployeeInput = {
  accept: Scalars['Boolean']['input'];
  profileId?: InputMaybe<Scalars['String']['input']>;
  token: Scalars['String']['input'];
};

export type ConfirmInviteInput = {
  accept: Scalars['Boolean']['input'];
  profileId: Scalars['String']['input'];
  token: Scalars['String']['input'];
};

export type ConfirmPatientInviteInput = {
  accept: Scalars['Boolean']['input'];
  patient: Scalars['String']['input'];
  token: Scalars['String']['input'];
};

export type ConsultType = {
  __typename?: 'ConsultType';
  consultation?: Maybe<Scalars['String']['output']>;
};

export type Consultation = {
  __typename?: 'Consultation';
  _id: Scalars['ID']['output'];
  appointmentAcceptedAt?: Maybe<Scalars['String']['output']>;
  appointmentStartedAt?: Maybe<Scalars['String']['output']>;
  companyId?: Maybe<Company>;
  consultationDuration?: Maybe<Scalars['String']['output']>;
  consultationOwner?: Maybe<Scalars['String']['output']>;
  contactMedium?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  createdThrough?: Maybe<Scalars['String']['output']>;
  declineReason?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  diagnosis?: Maybe<Array<Diag>>;
  discomfortLevel?: Maybe<Scalars['String']['output']>;
  disputeReason?: Maybe<Scalars['String']['output']>;
  disputeResolvedReason?: Maybe<Scalars['String']['output']>;
  disputeStatus?: Maybe<Scalars['String']['output']>;
  doctor?: Maybe<Doctor>;
  doctorEndCommunicationReason?: Maybe<Scalars['String']['output']>;
  doctorJoined?: Maybe<Scalars['Boolean']['output']>;
  doctorNote?: Maybe<Scalars['String']['output']>;
  doctorSatisfactionReason?: Maybe<Scalars['String']['output']>;
  doctorSatisfied?: Maybe<Scalars['Boolean']['output']>;
  externalPharmacy?: Maybe<Scalars['String']['output']>;
  externalProvider?: Maybe<Scalars['String']['output']>;
  fee?: Maybe<Scalars['Int']['output']>;
  firstNotice?: Maybe<Scalars['String']['output']>;
  followUpConsultationId?: Maybe<Scalars['JSON']['output']>;
  isDisputeResolved?: Maybe<Scalars['Boolean']['output']>;
  isDisputed?: Maybe<Scalars['Boolean']['output']>;
  isFollowUp?: Maybe<Scalars['Boolean']['output']>;
  isNemEnrollee?: Maybe<Scalars['Boolean']['output']>;
  joined?: Maybe<Scalars['Boolean']['output']>;
  notificationSchedules?: Maybe<Array<NotificationSchedule>>;
  paid?: Maybe<Scalars['Boolean']['output']>;
  path?: Maybe<Scalars['String']['output']>;
  patient?: Maybe<Profile>;
  patientEndCommunicationReason?: Maybe<Scalars['String']['output']>;
  patientJoined?: Maybe<Scalars['Boolean']['output']>;
  patientSatisfactionReason?: Maybe<Scalars['String']['output']>;
  patientSatisfied?: Maybe<Scalars['Boolean']['output']>;
  pharmacyAddress?: Maybe<Scalars['String']['output']>;
  pharmacyCode?: Maybe<Scalars['String']['output']>;
  pharmacyName?: Maybe<Scalars['String']['output']>;
  prescription?: Maybe<Array<Prescription>>;
  principalHmoId?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Rating>;
  reason?: Maybe<Scalars['String']['output']>;
  referralId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  symptoms?: Maybe<Array<Symp>>;
  time?: Maybe<Scalars['DateTime']['output']>;
  trackingId?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  wasDisputed?: Maybe<Scalars['Boolean']['output']>;
};

export type ConsultationChatMessageInput = {
  content: Scalars['String']['input'];
  receiver: Scalars['String']['input'];
  receiverRole: Scalars['String']['input'];
  sender: Scalars['String']['input'];
  senderRole: Scalars['String']['input'];
};

export type ConsultationCombinedPayload = {
  __typename?: 'ConsultationCombinedPayload';
  consultation?: Maybe<Consultation>;
  diagnosis?: Maybe<DiagnosticLabTest>;
  errors?: Maybe<Array<ErrorPayload>>;
  prescription?: Maybe<SinglePrescription>;
  referral?: Maybe<SingleReferral>;
};

export type ConsultationConnection = {
  __typename?: 'ConsultationConnection';
  data?: Maybe<Array<Consultation>>;
  pageInfo?: Maybe<PageInfo>;
};

export type ConsultationId = {
  id: Scalars['ID']['input'];
};

export type ConsultationPayload = {
  __typename?: 'ConsultationPayload';
  consultation?: Maybe<Consultation>;
  errors?: Maybe<Array<ErrorPayload>>;
};

export type Counts = {
  __typename?: 'Counts';
  activeCount?: Maybe<Array<Scalars['JSON']['output']>>;
  inactiveCount?: Maybe<Array<Scalars['JSON']['output']>>;
};

export type CreateAllergyInput = {
  food?: InputMaybe<Scalars['String']['input']>;
  medication?: InputMaybe<Scalars['String']['input']>;
  profile: Scalars['String']['input'];
  severity: Scalars['String']['input'];
};

export type CreateAppointmentInput = {
  date: Scalars['String']['input'];
  description: Scalars['String']['input'];
  doctor: Scalars['String']['input'];
  patient: Scalars['String']['input'];
  providerId: Scalars['String']['input'];
  time: Scalars['String']['input'];
};

export type CreateBusinessInput = {
  address: Scalars['String']['input'];
  name: Scalars['String']['input'];
  noOfEmployees: Scalars['Float']['input'];
  type: Scalars['String']['input'];
};

export type CreateBusinessPayload = {
  __typename?: 'CreateBusinessPayload';
  business?: Maybe<Business>;
};

export type CreateCompanyInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  externalId?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
  providerId: Scalars['String']['input'];
};

export type CreateConsultationInput = {
  consultationOwner: Scalars['String']['input'];
  contactMedium?: InputMaybe<Scalars['String']['input']>;
  createdThrough?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  discomfortLevel: Scalars['String']['input'];
  doctor?: InputMaybe<Scalars['String']['input']>;
  externalPharmacy?: InputMaybe<Scalars['String']['input']>;
  externalProvider?: InputMaybe<Scalars['String']['input']>;
  fee?: InputMaybe<Scalars['Float']['input']>;
  firstNotice: Scalars['String']['input'];
  followUpConsultationId?: InputMaybe<Scalars['String']['input']>;
  isFollowUp?: InputMaybe<Scalars['Boolean']['input']>;
  joined?: InputMaybe<Scalars['Boolean']['input']>;
  paid?: InputMaybe<Scalars['Boolean']['input']>;
  path?: InputMaybe<Scalars['String']['input']>;
  patient: Scalars['String']['input'];
  pharmacyAddress?: InputMaybe<Scalars['String']['input']>;
  pharmacyCode?: InputMaybe<Scalars['String']['input']>;
  pharmacyName?: InputMaybe<Scalars['String']['input']>;
  providerId?: InputMaybe<Scalars['String']['input']>;
  referral?: InputMaybe<Scalars['Boolean']['input']>;
  referralId?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  symptoms: Array<Symptom>;
  time?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type CreateCurrentIllness = {
  __typename?: 'CreateCurrentIllness';
  _id: Scalars['ID']['output'];
  abilityToFunction: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  doctor: Scalars['ID']['output'];
  howLongDoesItLast: Scalars['String']['output'];
  intensity: Scalars['String']['output'];
  locationOfProblem: Scalars['String']['output'];
  otherPain: Scalars['String']['output'];
  patient: Scalars['ID']['output'];
  problemWorse: Scalars['String']['output'];
  scaleOfDiscomfort: Scalars['String']['output'];
  timeOfFirstNotice: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type CreateCurrentIllnessInput = {
  abilityToFunction: Scalars['String']['input'];
  doctor: Scalars['ID']['input'];
  howLongDoesItLast: Scalars['String']['input'];
  intensity: Scalars['String']['input'];
  locationOfProblem: Scalars['String']['input'];
  otherPain: Scalars['String']['input'];
  patient: Scalars['ID']['input'];
  problemWorse: Scalars['String']['input'];
  scaleOfDiscomfort: Scalars['String']['input'];
  timeOfFirstNotice: Scalars['String']['input'];
};

export type CreateDisputeInput = {
  disputeReason: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type CreateDoctorPatientInput = {
  doctor: Scalars['String']['input'];
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
  patient?: InputMaybe<Scalars['String']['input']>;
};

export type CreateDoctorProfileAccountInput = {
  cadre: Scalars['String']['input'];
  dob?: InputMaybe<Scalars['DateTime']['input']>;
  email: Scalars['String']['input'];
  fee?: InputMaybe<Scalars['Float']['input']>;
  firstName: Scalars['String']['input'];
  gender?: InputMaybe<Scalars['String']['input']>;
  hospital?: InputMaybe<Scalars['String']['input']>;
  idCard?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  lastName: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
  providerId: Scalars['String']['input'];
  specialization: Scalars['String']['input'];
  timezoneOffset?: InputMaybe<Scalars['String']['input']>;
};

export type CreateDoctorProfileInput = {
  cadre: Scalars['String']['input'];
  dob?: InputMaybe<Scalars['DateTime']['input']>;
  dociId: Scalars['String']['input'];
  fee?: InputMaybe<Scalars['Float']['input']>;
  firstName: Scalars['String']['input'];
  gender?: InputMaybe<Scalars['String']['input']>;
  hospital?: InputMaybe<Scalars['String']['input']>;
  idCard?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  lastName: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
  providerId: Scalars['String']['input'];
  specialization?: InputMaybe<Scalars['String']['input']>;
  timezoneOffset?: InputMaybe<Scalars['String']['input']>;
};

export type CreateEarningInput = {
  balance: Scalars['Float']['input'];
  consultationId?: InputMaybe<Scalars['String']['input']>;
  doctor: Scalars['String']['input'];
  providerId?: InputMaybe<Scalars['String']['input']>;
};

export type CreateEnrolleeInput = {
  client?: InputMaybe<Scalars['String']['input']>;
  companyId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  expiryDate: Scalars['DateTime']['input'];
  firstName: Scalars['String']['input'];
  hmoId: Scalars['String']['input'];
  isMicroInsurance?: InputMaybe<Scalars['Boolean']['input']>;
  lastName: Scalars['String']['input'];
  noc?: InputMaybe<Scalars['Int']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  photo?: InputMaybe<Scalars['String']['input']>;
  plan?: InputMaybe<Scalars['String']['input']>;
  planId?: InputMaybe<Scalars['String']['input']>;
  providerId: Scalars['String']['input'];
};

export type CreateEnterpriseInput = {
  address: Scalars['String']['input'];
  businessType: Scalars['String']['input'];
  dateFounded?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  image: Scalars['String']['input'];
  industry: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  name: Scalars['String']['input'];
  noOfEmployees: Scalars['Float']['input'];
  phoneNumber: Scalars['String']['input'];
  planId: Scalars['String']['input'];
  startDate?: InputMaybe<Scalars['String']['input']>;
};

export type CreateFamilyInput = {
  admin: Scalars['String']['input'];
  dob: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName: Scalars['String']['input'];
  gender: Scalars['String']['input'];
  image?: InputMaybe<Scalars['String']['input']>;
  lastName: Scalars['String']['input'];
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
  relationship: Scalars['String']['input'];
};

export type CreateIllnessInput = {
  description: Scalars['String']['input'];
  name: Scalars['String']['input'];
};

export type CreateInsurancePlanInput = {
  amount: Scalars['Float']['input'];
  benefits?: InputMaybe<Array<BenefitInput>>;
  code: Scalars['String']['input'];
  description: Scalars['String']['input'];
  duration: Scalars['String']['input'];
  externalProviderName: Scalars['String']['input'];
  image?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type CreateLabResultInput = {
  doctor: Scalars['String']['input'];
  partner: Scalars['String']['input'];
  patient: Scalars['String']['input'];
  url: Scalars['String']['input'];
};

export type CreateMessageInput = {
  body: Scalars['String']['input'];
  recipient: Scalars['String']['input'];
  sender: Scalars['String']['input'];
  subject: Scalars['String']['input'];
};

export type CreatePatientMedicationInput = {
  doctor: Scalars['String']['input'];
  dosage: Scalars['Float']['input'];
  interval: Scalars['String']['input'];
  name: Scalars['String']['input'];
  patient: Scalars['String']['input'];
};

export type CreatePatientProfileInput = {
  bloodGroup?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['String']['input']>;
  dociId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName: Scalars['String']['input'];
  gender: Scalars['String']['input'];
  genotype?: InputMaybe<Scalars['String']['input']>;
  height?: InputMaybe<Scalars['Int']['input']>;
  hmoId?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  lastName: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
  providerId: Scalars['ID']['input'];
  roleId: Scalars['ID']['input'];
  timezoneOffset?: InputMaybe<Scalars['String']['input']>;
  weight?: InputMaybe<Scalars['Int']['input']>;
};

export type CreatePayoutInput = {
  amount: Scalars['Float']['input'];
  doctor: Scalars['String']['input'];
  providerId?: InputMaybe<Scalars['String']['input']>;
  status: Scalars['String']['input'];
};

export type CreatePayoutsInput = {
  providerId: Scalars['String']['input'];
};

export type CreatePermissionInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type CreatePlanInput = {
  allowedFeatures?: InputMaybe<Scalars['JSON']['input']>;
  amount: Scalars['Float']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  duration?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  noOfDoctors?: InputMaybe<Scalars['Float']['input']>;
  provider?: InputMaybe<Scalars['ID']['input']>;
  specialisation?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type CreateProfile = {
  __typename?: 'CreateProfile';
  _id: Scalars['ID']['output'];
  accountId?: Maybe<Scalars['ID']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  bloodGroup?: Maybe<Scalars['String']['output']>;
  consultations?: Maybe<Scalars['Int']['output']>;
  createdAt: Scalars['DateTimeScalar']['output'];
  dob?: Maybe<Scalars['DateTime']['output']>;
  dociId: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  externalPlanCode?: Maybe<Scalars['String']['output']>;
  externalPlanType?: Maybe<Scalars['String']['output']>;
  externalProvider?: Maybe<Scalars['String']['output']>;
  externalProviderId?: Maybe<Scalars['String']['output']>;
  firstName: Scalars['String']['output'];
  gender?: Maybe<Scalars['String']['output']>;
  genotype?: Maybe<Scalars['String']['output']>;
  height?: Maybe<Scalars['Float']['output']>;
  hmoId?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  lastName: Scalars['String']['output'];
  pastIllness?: Maybe<Array<PastIllnessType>>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscriptionId?: Maybe<Scalars['ID']['output']>;
  timezoneOffset?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTimeScalar']['output'];
  weight?: Maybe<Scalars['Float']['output']>;
};

export type CreateProfileInput = {
  bloodGroup?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['String']['input']>;
  firstName: Scalars['String']['input'];
  gender: Scalars['String']['input'];
  genotype?: InputMaybe<Scalars['String']['input']>;
  height?: InputMaybe<Scalars['Int']['input']>;
  hmoId?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  lastName: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
  timezoneOffset?: InputMaybe<Scalars['String']['input']>;
  weight?: InputMaybe<Scalars['Int']['input']>;
};

export type CreateProviderInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  consultationLimitType?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  hmoPlans?: InputMaybe<Array<HmoPlansInput>>;
  icon?: InputMaybe<Scalars['String']['input']>;
  iconAlt?: InputMaybe<Scalars['String']['input']>;
  isWellaHealthIntegration?: InputMaybe<Scalars['Boolean']['input']>;
  monthlyConsultationLimit?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
  planId?: InputMaybe<Scalars['String']['input']>;
  rareCase?: InputMaybe<Scalars['Boolean']['input']>;
  userTypeId: Scalars['String']['input'];
};

export type CreateReminderInput = {
  date: Scalars['DateTime']['input'];
  description: Scalars['String']['input'];
  interval?: InputMaybe<Scalars['String']['input']>;
  patient: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type CreateRequestInput = {
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  location: Scalars['String']['input'];
  name: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type CreateRoleInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  editable?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  permissions: Array<Scalars['String']['input']>;
};

export type CreateSettingInput = {
  key?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  value: Scalars['String']['input'];
};

export type CreateTicketInput = {
  createdBy: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  priority?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  title: Scalars['String']['input'];
  type?: InputMaybe<Scalars['String']['input']>;
};

export type CreateUserTypeInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  icon?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type CreateVerificationInput = {
  alumni_association: AlumniAssociation;
  external_reference: ExternalReference;
  license: License;
  profileId: Scalars['String']['input'];
  qualification: Qualification;
  reference: Reference;
  status?: InputMaybe<Scalars['Boolean']['input']>;
  yearbook: Yearbook;
};

export type CurrentIllness = {
  __typename?: 'CurrentIllness';
  _id?: Maybe<Scalars['ID']['output']>;
  abilityToFunction?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  doctor: Scalars['String']['output'];
  howLongDoesItLast?: Maybe<Scalars['String']['output']>;
  intensity?: Maybe<Scalars['String']['output']>;
  locationOfProblem?: Maybe<Scalars['String']['output']>;
  otherPain?: Maybe<Scalars['String']['output']>;
  patient: Scalars['String']['output'];
  problemWorse?: Maybe<Scalars['String']['output']>;
  scaleOfDiscomfort?: Maybe<Scalars['String']['output']>;
  timeOfFirstNotice?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTimeScalar']['output']>;
};

export type CurrentIllnessPayload = {
  __typename?: 'CurrentIllnessPayload';
  currentIllness?: Maybe<CurrentIllness>;
  errors?: Maybe<Array<ErrorPayload>>;
};

export type DaysStats = {
  __typename?: 'DaysStats';
  active?: Maybe<Scalars['Float']['output']>;
  fiveDays?: Maybe<Counts>;
  inactive?: Maybe<Scalars['Float']['output']>;
  oneDay?: Maybe<Counts>;
  oneMonth?: Maybe<Counts>;
  oneYear?: Maybe<Counts>;
  threeMonths?: Maybe<Counts>;
};

export type DeactivateCompanyEnrolleeInput = {
  companyId: Scalars['String']['input'];
};

export type DeactivateEnrolleeInput = {
  id: Scalars['String']['input'];
};

export type DeclineConsultationInput = {
  id: Scalars['ID']['input'];
  reason: Scalars['ID']['input'];
};

export type DeleteAllergyInput = {
  id: Scalars['String']['input'];
};

export type DeleteAvailableTimePayload = {
  __typename?: 'DeleteAvailableTimePayload';
  availability?: Maybe<Availability>;
  errors?: Maybe<Array<ErrorPayload>>;
};

export type DeleteConsultationPayload = {
  __typename?: 'DeleteConsultationPayload';
  count?: Maybe<Scalars['Int']['output']>;
  errors?: Maybe<Array<ErrorPayload>>;
  message?: Maybe<Scalars['String']['output']>;
};

export type DeleteDoctorPatientPayload = {
  __typename?: 'DeleteDoctorPatientPayload';
  count?: Maybe<Scalars['Int']['output']>;
  errors?: Maybe<Array<ErrorPayload>>;
  message?: Maybe<Scalars['String']['output']>;
};

export type DeleteEnrolleeInput = {
  id: Scalars['String']['input'];
};

export type DeleteFamilyInput = {
  id: Scalars['String']['input'];
};

export type DeleteLabInput = {
  id: Scalars['String']['input'];
};

export type DeleteMyAccountInput = {
  deactivateType: Scalars['String']['input'];
  deleteReason: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type DeletePermissionPayload = {
  __typename?: 'DeletePermissionPayload';
  count: Scalars['Float']['output'];
  errors: Array<ErrorPayload>;
  message: Scalars['String']['output'];
};

export type DeletePlanInput = {
  id: Scalars['String']['input'];
};

export type DeleteProfileInput = {
  id: Scalars['String']['input'];
};

export type DeleteProviderInput = {
  id: Scalars['ID']['input'];
};

export type DeleteProviderPayload = {
  __typename?: 'DeleteProviderPayload';
  count: Scalars['Float']['output'];
  message: Scalars['String']['output'];
};

export type DeleteRoleInput = {
  id: Scalars['String']['input'];
};

export type DeleteSettingInput = {
  id: Scalars['String']['input'];
};

export type DeleteSettingPayload = {
  __typename?: 'DeleteSettingPayload';
  count: Scalars['Float']['output'];
  errors: Array<ErrorPayload>;
  message: Scalars['String']['output'];
};

export type DeleteUserTypeInput = {
  id: Scalars['String']['input'];
};

export type Dependant = {
  __typename?: 'Dependant';
  dob?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  relationship?: Maybe<Scalars['String']['output']>;
};

export type Diag = {
  __typename?: 'Diag';
  ailment: Scalars['String']['output'];
  severity: Scalars['String']['output'];
};

export type Diagnosis = {
  ailment: Scalars['String']['input'];
  severity: Scalars['String']['input'];
};

export type DiagnosisConnection = {
  __typename?: 'DiagnosisConnection';
  diagnosis: Array<SymptomsDiagnosis>;
};

export type DiagnosticCalendarPayload = {
  __typename?: 'DiagnosticCalendarPayload';
  month?: Maybe<Scalars['Float']['output']>;
  sum?: Maybe<Scalars['Float']['output']>;
  year?: Maybe<Scalars['Float']['output']>;
};

export type DiagnosticDashboard = {
  __typename?: 'DiagnosticDashboard';
  cancelledTestsCount?: Maybe<Scalars['Float']['output']>;
  cancelledTestsStats?: Maybe<Array<DiagnosticCalendarPayload>>;
  completedTestsCount?: Maybe<Scalars['Float']['output']>;
  completedTestsStats?: Maybe<Array<DiagnosticCalendarPayload>>;
  scheduledTestsCount?: Maybe<Scalars['Float']['output']>;
  scheduledTestsStats?: Maybe<Array<DiagnosticCalendarPayload>>;
  testRequestsCount?: Maybe<Scalars['Float']['output']>;
  testRequestsStats?: Maybe<Array<DiagnosticCalendarPayload>>;
};

export type DiagnosticInput = {
  id: Scalars['String']['input'];
};

export type DiagnosticLabTest = {
  __typename?: 'DiagnosticLabTest';
  _id?: Maybe<Scalars['ID']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  markedUpTestPrice?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  paid?: Maybe<Scalars['Boolean']['output']>;
  partner?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
  tat?: Maybe<Scalars['String']['output']>;
  testPriceMarkUp?: Maybe<Scalars['Float']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  urgency?: Maybe<Scalars['String']['output']>;
};

export type DiagnosticLabTestInput = {
  name: Scalars['String']['input'];
  note?: InputMaybe<Scalars['String']['input']>;
  price: Scalars['Float']['input'];
  tat: Scalars['String']['input'];
  urgency?: InputMaybe<Scalars['String']['input']>;
};

export type DiagnosticLabTestPayload = {
  __typename?: 'DiagnosticLabTestPayload';
  diagnosticLabTest?: Maybe<DiagnosticLabTest>;
};

export type DiagnosticLabTestsConnection = {
  __typename?: 'DiagnosticLabTestsConnection';
  data?: Maybe<Array<DiagnosticLabTest>>;
  pageInfo?: Maybe<PageInfo>;
};

export type DiagnosticTest = {
  __typename?: 'DiagnosticTest';
  _id: Scalars['ID']['output'];
  cancellationReason?: Maybe<Scalars['String']['output']>;
  consultationId?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  diagnosticReferral?: Maybe<Scalars['String']['output']>;
  doctor?: Maybe<DoctorProfile>;
  labInfo?: Maybe<LabInfo>;
  note?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Partner>;
  patient?: Maybe<Profile>;
  provisionalDiagnosis?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  referralId?: Maybe<Scalars['String']['output']>;
  sampleCollection?: Maybe<Scalars['String']['output']>;
  scheduledAt?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  testId?: Maybe<Scalars['String']['output']>;
  testPickUpFee?: Maybe<Scalars['Float']['output']>;
  testResults?: Maybe<Array<TestResults>>;
  tests?: Maybe<Array<DiagnosticLabTest>>;
  time?: Maybe<Scalars['String']['output']>;
  total?: Maybe<Scalars['Float']['output']>;
  trackingId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userLocation?: Maybe<Location>;
};

export type DiagnosticTestPayload = {
  __typename?: 'DiagnosticTestPayload';
  diagnosticTest?: Maybe<DiagnosticTest>;
};

export type DiagnosticTestsConnection = {
  __typename?: 'DiagnosticTestsConnection';
  data?: Maybe<Array<DiagnosticTest>>;
  pageInfo?: Maybe<PageInfo>;
};

export type DisableAccountInput = {
  deactivateType: Scalars['String']['input'];
};

export type Doctor = {
  __typename?: 'Doctor';
  _id?: Maybe<Scalars['String']['output']>;
  accountDetails?: Maybe<AccountDetail>;
  balance?: Maybe<Scalars['Float']['output']>;
  cadre?: Maybe<Scalars['String']['output']>;
  dob?: Maybe<Scalars['DateTime']['output']>;
  dociId: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  hospital?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  picture?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['ID']['output']>;
  rating?: Maybe<Scalars['Float']['output']>;
  specialization?: Maybe<Scalars['String']['output']>;
  timezoneOffset?: Maybe<Scalars['String']['output']>;
};

export type DoctorAvailabilityStat = {
  __typename?: 'DoctorAvailabilityStat';
  availability?: Maybe<Availability>;
  dociId?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
};

export type DoctorPatient = {
  __typename?: 'DoctorPatient';
  _id: Scalars['ID']['output'];
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<DoctorProfile>;
  doctorData?: Maybe<Scalars['JSON']['output']>;
  email?: Maybe<Scalars['EmailAddress']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  patient?: Maybe<Profile>;
  patientData?: Maybe<Scalars['JSON']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  tokenExpiresAt?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type DoctorPatientConnection = {
  __typename?: 'DoctorPatientConnection';
  data: Array<DoctorPatient>;
  errors?: Maybe<Array<ErrorPayload>>;
  pageInfo?: Maybe<PageInfo>;
};

export type DoctorPatientIdInput = {
  id: Scalars['String']['input'];
};

export type DoctorPatientPayload = {
  __typename?: 'DoctorPatientPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  patient?: Maybe<DoctorPatient>;
};

export type DoctorProfile = {
  __typename?: 'DoctorProfile';
  _id?: Maybe<Scalars['ID']['output']>;
  accountDetails?: Maybe<AccountDetails>;
  accountId?: Maybe<Account>;
  address?: Maybe<Scalars['String']['output']>;
  balance?: Maybe<Scalars['Int']['output']>;
  cadre?: Maybe<Scalars['String']['output']>;
  consultationReminderNotification?: Maybe<Scalars['Boolean']['output']>;
  consultationRequestNotification?: Maybe<Scalars['Boolean']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  dob?: Maybe<Scalars['DateTime']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  fee?: Maybe<Scalars['Float']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  hospital?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  instantConsultationNotification?: Maybe<Scalars['Boolean']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  picture?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<ProviderType>;
  rating?: Maybe<Scalars['Int']['output']>;
  specialization?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type DoctorProfileArrayPayload = {
  __typename?: 'DoctorProfileArrayPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  pageInfo?: Maybe<PageInfo>;
  profile?: Maybe<Array<DoctorProfile>>;
};

export type DoctorProfileCountInput = {
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};

export type DoctorProfileInput = {
  id: Scalars['String']['input'];
};

export type DoctorProfilePayload = {
  __typename?: 'DoctorProfilePayload';
  profile?: Maybe<DoctorProfile>;
};

export type DoctorThreshold = {
  __typename?: 'DoctorThreshold';
  _id?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  end?: Maybe<Scalars['Float']['output']>;
  priority?: Maybe<Scalars['Float']['output']>;
  start?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type DosageFreq = {
  __typename?: 'DosageFreq';
  duration?: Maybe<Scalars['Float']['output']>;
  timing?: Maybe<Scalars['Float']['output']>;
};

export type DosageFrequency = {
  duration?: InputMaybe<Scalars['Float']['input']>;
  timing?: InputMaybe<Scalars['Float']['input']>;
};

export type DosageFrequencyInput = {
  duration?: InputMaybe<Scalars['Float']['input']>;
  timing?: InputMaybe<Scalars['Float']['input']>;
};

export type Drug = {
  __typename?: 'Drug';
  _id?: Maybe<Scalars['String']['output']>;
  amount?: Maybe<Scalars['Float']['output']>;
  approved?: Maybe<Scalars['String']['output']>;
  dosageFrequency?: Maybe<ReferralDosageFrequency>;
  dosageQuantity?: Maybe<Scalars['Float']['output']>;
  dosageUnit?: Maybe<Scalars['String']['output']>;
  drugForm?: Maybe<Scalars['String']['output']>;
  drugName?: Maybe<Scalars['String']['output']>;
  drugPrice?: Maybe<Scalars['Float']['output']>;
  instructions?: Maybe<Scalars['String']['output']>;
  markedUpPrice?: Maybe<Scalars['Float']['output']>;
  markup?: Maybe<Scalars['Float']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  paid?: Maybe<Scalars['Boolean']['output']>;
  priceListId?: Maybe<Scalars['Float']['output']>;
  quantity?: Maybe<Scalars['Float']['output']>;
  route?: Maybe<Scalars['String']['output']>;
  unitPrice?: Maybe<Scalars['Float']['output']>;
};

export type DrugInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  dosageFrequency?: InputMaybe<DosageFrequencyInput>;
  dosageQuantity?: InputMaybe<Scalars['Float']['input']>;
  dosageUnit?: InputMaybe<Scalars['String']['input']>;
  drugForm?: InputMaybe<Scalars['String']['input']>;
  drugName: Scalars['String']['input'];
  drugPrice?: InputMaybe<Scalars['Float']['input']>;
  instructions?: InputMaybe<Scalars['String']['input']>;
  markedUpPrice?: InputMaybe<Scalars['Float']['input']>;
  markup?: InputMaybe<Scalars['Float']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  priceListId?: InputMaybe<Scalars['Float']['input']>;
  quantity?: InputMaybe<Scalars['Float']['input']>;
  route?: InputMaybe<Scalars['String']['input']>;
  unitPrice?: InputMaybe<Scalars['Float']['input']>;
};

export type DrugOrder = {
  __typename?: 'DrugOrder';
  _id?: Maybe<Scalars['ID']['output']>;
  cancellationReason?: Maybe<Scalars['String']['output']>;
  consultationId?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deliveryFee?: Maybe<Scalars['Float']['output']>;
  deliveryOption?: Maybe<Scalars['String']['output']>;
  doctor?: Maybe<DoctorProfile>;
  note?: Maybe<Scalars['String']['output']>;
  orderId?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Partner>;
  patient?: Maybe<PatientProfile>;
  pharmacyAddress?: Maybe<Scalars['String']['output']>;
  pharmacyCode?: Maybe<Scalars['String']['output']>;
  pharmacyName?: Maybe<Scalars['String']['output']>;
  prescriptionDate?: Maybe<Scalars['DateTime']['output']>;
  prescriptions?: Maybe<Array<DrugPrescription>>;
  status?: Maybe<Scalars['String']['output']>;
  total?: Maybe<Scalars['Float']['output']>;
  trackingId?: Maybe<Scalars['String']['output']>;
  trackingLink?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userLocation?: Maybe<Location>;
};

export type DrugOrderInput = {
  id: Scalars['String']['input'];
};

export type DrugOrderPayload = {
  __typename?: 'DrugOrderPayload';
  drugOrder: DrugOrder;
  errors: Array<ErrorPayload>;
};

export type DrugOrdersConnection = {
  __typename?: 'DrugOrdersConnection';
  data: Array<DrugOrder>;
  pageInfo: PageInfo;
};

export type DrugPrescription = {
  __typename?: 'DrugPrescription';
  dosageFrequency?: Maybe<Scalars['JSON']['output']>;
  dosageQuantity?: Maybe<Scalars['Float']['output']>;
  dosageUnit?: Maybe<Scalars['String']['output']>;
  drugForm?: Maybe<Scalars['String']['output']>;
  drugName?: Maybe<Scalars['String']['output']>;
  drugPrice?: Maybe<Scalars['Float']['output']>;
  drugPriceMarkUp?: Maybe<Scalars['Float']['output']>;
  markedUpDrugPrice?: Maybe<Scalars['Float']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  priceListId?: Maybe<Scalars['Float']['output']>;
  quantity?: Maybe<Scalars['Float']['output']>;
  unitPrice?: Maybe<Scalars['Float']['output']>;
};

export type DrugsApprovals = {
  __typename?: 'DrugsApprovals';
  _id?: Maybe<Scalars['String']['output']>;
  consultation?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<Scalars['String']['output']>;
  drugs?: Maybe<Array<Drug>>;
  note?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Scalars['String']['output']>;
  patient?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  referral?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type DrugsApprovalsPayload = {
  __typename?: 'DrugsApprovalsPayload';
  data?: Maybe<Array<PopulatedDrugsApprovals>>;
  pageInfo?: Maybe<PageInfo>;
};

export type DrugsInput = {
  drugName?: InputMaybe<Scalars['String']['input']>;
  drugPrice?: InputMaybe<Scalars['Float']['input']>;
  drugPriceMarkUp?: InputMaybe<Scalars['Float']['input']>;
  markedUpDrugPrice?: InputMaybe<Scalars['Float']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  paid?: InputMaybe<Scalars['Boolean']['input']>;
  priceListId?: InputMaybe<Scalars['Float']['input']>;
  quantity?: InputMaybe<Scalars['Float']['input']>;
  unitPrice?: InputMaybe<Scalars['Float']['input']>;
};

export type Earning = {
  __typename?: 'Earning';
  _id: Scalars['ID']['output'];
  balance?: Maybe<Scalars['Float']['output']>;
  consultationId?: Maybe<Consultation>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  disputed?: Maybe<Scalars['Boolean']['output']>;
  doctor?: Maybe<DoctorProfile>;
  providerId?: Maybe<Provider>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type EarningConnection = {
  __typename?: 'EarningConnection';
  data: Array<Earning>;
  errors?: Maybe<Array<ErrorPayload>>;
  pageInfo?: Maybe<PageInfo>;
  totalEarnings?: Maybe<Scalars['Float']['output']>;
  totalPayouts?: Maybe<Scalars['Float']['output']>;
};

export type EarningPayload = {
  __typename?: 'EarningPayload';
  earning?: Maybe<Earning>;
  errors?: Maybe<Array<ErrorPayload>>;
};

export type EarningsStat = {
  __typename?: 'EarningsStat';
  earningData: Scalars['JSON']['output'];
  payoutData: Scalars['JSON']['output'];
  subscriptionIncome: Scalars['Int']['output'];
  subscriptionIncomeData: Scalars['JSON']['output'];
  totalEarnings: Scalars['Int']['output'];
  totalPayout: Scalars['Int']['output'];
};

export type EditEmployeeInput = {
  dob?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  noofdependants?: InputMaybe<Scalars['Float']['input']>;
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type EmailOutput = {
  __typename?: 'EmailOutput';
  _id: Scalars['ID']['output'];
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['EmailAddress']['output']>;
  profileData?: Maybe<Scalars['JSONObject']['output']>;
  role?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type Employee = {
  __typename?: 'Employee';
  _id?: Maybe<Scalars['String']['output']>;
  admin?: Maybe<Profile>;
  businessId?: Maybe<Business>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  dependant?: Maybe<Scalars['Boolean']['output']>;
  dependants?: Maybe<Array<Dependant>>;
  dob?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  noofdependants?: Maybe<Scalars['Float']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  profileId?: Maybe<Profile>;
  relationship?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type EmployeeDependants = {
  __typename?: 'EmployeeDependants';
  employees?: Maybe<Array<Employee>>;
};

export type EmployeeInput = {
  employeeId: Scalars['String']['input'];
};

export type EndConsultationInput = {
  doctorEndCommunicationReason?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  messages?: InputMaybe<Array<ConsultationChatMessageInput>>;
  patientEndCommunicationReason?: InputMaybe<Scalars['String']['input']>;
};

export type Enrollee = {
  __typename?: 'Enrollee';
  _id: Scalars['ID']['output'];
  accessPlanName?: Maybe<Scalars['String']['output']>;
  client?: Maybe<Scalars['String']['output']>;
  companyId?: Maybe<Company>;
  deactivated?: Maybe<Scalars['Boolean']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  expiryDate?: Maybe<Scalars['DateTime']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  hmoId?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  noc?: Maybe<Scalars['Int']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  photo?: Maybe<Scalars['String']['output']>;
  plan?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Plan>;
  providerId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Boolean']['output']>;
};

export type EnrolleeDeactivatedPayload = {
  __typename?: 'EnrolleeDeactivatedPayload';
  count: Scalars['Float']['output'];
  errors: Array<ErrorPayload>;
};

export type EnrolleeOrderInput = {
  consultationId: Scalars['String']['input'];
  deliveryOption: Scalars['String']['input'];
  doctor: Scalars['String']['input'];
  drugIds: Array<Scalars['String']['input']>;
  note?: InputMaybe<Scalars['String']['input']>;
  patient: Scalars['String']['input'];
  pharmacyAddress?: InputMaybe<Scalars['String']['input']>;
  pharmacyCode?: InputMaybe<Scalars['String']['input']>;
  pharmacyName?: InputMaybe<Scalars['String']['input']>;
  prescriptionDate?: InputMaybe<Scalars['String']['input']>;
  userLocation?: InputMaybe<UserLocationInput>;
};

export type EnrolleePayload = {
  __typename?: 'EnrolleePayload';
  enrollee?: Maybe<Enrollee>;
  message: Scalars['String']['output'];
};

export type EnrolleesConnection = {
  __typename?: 'EnrolleesConnection';
  data: Array<Enrollee>;
  pageInfo: PageInfo;
};

export type ErrorPayload = {
  __typename?: 'ErrorPayload';
  field: Scalars['String']['output'];
  message: Array<Scalars['String']['output']>;
};

export type ExportDetails = {
  __typename?: 'ExportDetails';
  fileUrl?: Maybe<Scalars['String']['output']>;
};

export type ExternalPlan = {
  __typename?: 'ExternalPlan';
  _id?: Maybe<Scalars['String']['output']>;
  allowedFeatures?: Maybe<AllowedFeatures>;
  amount?: Maybe<Scalars['Float']['output']>;
  benefits?: Maybe<Array<BenefitPayload>>;
  billingDayOffset?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  duration?: Maybe<Scalars['String']['output']>;
  external?: Maybe<Scalars['Boolean']['output']>;
  externalProviderName?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  noOfDoctors?: Maybe<Scalars['Float']['output']>;
  planCode?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  specialisation?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscribed?: Maybe<Scalars['Boolean']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type ExternalPlanInput = {
  id: Scalars['String']['input'];
};

export type ExternalReference = {
  doctor_email: Scalars['String']['input'];
  doctor_institution: Scalars['String']['input'];
  doctor_name: Scalars['String']['input'];
  doctor_position: Scalars['String']['input'];
};

export type Family = {
  __typename?: 'Family';
  _id: Scalars['ID']['output'];
  admin?: Maybe<PatientProfile>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  dob?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  profileId?: Maybe<PatientProfile>;
  relationship?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  token: Scalars['String']['output'];
  tokenExpiresAt: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type FamilyArrayPayload = {
  __typename?: 'FamilyArrayPayload';
  family: Array<Family>;
  message: Scalars['String']['output'];
  pageInfo: PageInfo;
};

export type FamilyInput = {
  id: Scalars['String']['input'];
};

export type FamilyPayload = {
  __typename?: 'FamilyPayload';
  family?: Maybe<Family>;
  message?: Maybe<Scalars['String']['output']>;
};

export type FavouriteDoctor = {
  __typename?: 'FavouriteDoctor';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  doctor?: Maybe<Scalars['JSON']['output']>;
  patient: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type FavouriteDoctorInput = {
  doctor: Scalars['String']['input'];
  patient: Scalars['String']['input'];
};

export type FavouriteDoctorPayload = {
  __typename?: 'FavouriteDoctorPayload';
  favouriteDoctor: FavouriteDoctor;
};

export type FavouriteDoctorsConnection = {
  __typename?: 'FavouriteDoctorsConnection';
  data: Array<FavouriteDoctor>;
  pageInfo: PageInfo;
};

export type FcmNotificationPayload = {
  __typename?: 'FcmNotificationPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  notification?: Maybe<FcmNotificationResponse>;
};

export type FcmNotificationResponse = {
  __typename?: 'FcmNotificationResponse';
  failedTokens?: Maybe<Array<Scalars['String']['output']>>;
  failureCount?: Maybe<Scalars['String']['output']>;
  multicastId?: Maybe<Scalars['String']['output']>;
  notificationId?: Maybe<Scalars['String']['output']>;
  successCount?: Maybe<Scalars['String']['output']>;
};

export type FcmToken = {
  __typename?: 'FcmToken';
  _id?: Maybe<Scalars['ID']['output']>;
  createdAt?: Maybe<Scalars['String']['output']>;
  deviceId?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['String']['output']>;
  user?: Maybe<Scalars['String']['output']>;
};

export type FilterInputType = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Float']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type FilterPlansInput = {
  endMonth?: InputMaybe<Scalars['Float']['input']>;
  planId: Scalars['String']['input'];
  startMonth?: InputMaybe<Scalars['Float']['input']>;
  year?: InputMaybe<Scalars['Float']['input']>;
};

export type FilterRegistrationsInput = {
  endMonth?: InputMaybe<Scalars['Float']['input']>;
  providerId?: InputMaybe<Scalars['String']['input']>;
  startMonth?: InputMaybe<Scalars['Float']['input']>;
  year?: InputMaybe<Scalars['Float']['input']>;
};

export type FinalizeInput = {
  id: Scalars['String']['input'];
  ids?: InputMaybe<Array<Scalars['String']['input']>>;
  partner: Scalars['String']['input'];
};

export type FinalizeSpecialistInput = {
  id: Scalars['String']['input'];
  ids?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type FindProfilesIdsInput = {
  ids: Array<Scalars['String']['input']>;
};

export type GenerateConsultationStatsInput = {
  email: Scalars['String']['input'];
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  providerId: Scalars['String']['input'];
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
};

export type GenerateHmoIdInput = {
  email: Scalars['String']['input'];
  externalProviderId: Scalars['Float']['input'];
};

export type GenerateHmoInput = {
  email: Scalars['String']['input'];
};

export type GetBusinessEmployeesInput = {
  businessId: Scalars['String']['input'];
};

export type GetBusinessInput = {
  businessId: Scalars['String']['input'];
};

export type GetDiagnosticDashboardInput = {
  partner: Scalars['String']['input'];
};

export type GetFeaturedPartnerInput = {
  category: Scalars['String']['input'];
};

export type GetPartnerConfigurationInput = {
  category?: InputMaybe<Scalars['String']['input']>;
  partner: Scalars['String']['input'];
};

export type GetPartnerSubdomainInput = {
  subdomain: Scalars['String']['input'];
};

export type GetPharmacyDashboardInput = {
  partner: Scalars['String']['input'];
};

export type GetReferralInput = {
  id: Scalars['String']['input'];
};

export type GetSubscriptionInput = {
  email?: InputMaybe<Scalars['String']['input']>;
};

export type GetVerificationInput = {
  id: Scalars['String']['input'];
};

export type HmoIdInput = {
  hmoId: Scalars['String']['input'];
};

export type HmoPlanType = {
  __typename?: 'HmoPlanType';
  name?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
};

export type HmoPlans = {
  __typename?: 'HmoPlans';
  name?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
};

export type HmoPlansInput = {
  name?: InputMaybe<Scalars['String']['input']>;
  planId?: InputMaybe<Scalars['String']['input']>;
};

export type HospitalDrug = {
  __typename?: 'HospitalDrug';
  _id?: Maybe<Scalars['String']['output']>;
  drugName?: Maybe<Scalars['String']['output']>;
  drugPrice?: Maybe<Scalars['Float']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Scalars['String']['output']>;
  priceListId?: Maybe<Scalars['Float']['output']>;
  quantity?: Maybe<Scalars['String']['output']>;
  unitPrice?: Maybe<Scalars['Float']['output']>;
};

export type HospitalMedication = {
  __typename?: 'HospitalMedication';
  _id: Scalars['ID']['output'];
  cancellationReason?: Maybe<Scalars['String']['output']>;
  consultationId?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  deliveryFee?: Maybe<Scalars['Float']['output']>;
  deliveryOption: Scalars['String']['output'];
  doctor?: Maybe<DoctorProfile>;
  drugs: Array<HospitalDrug>;
  note?: Maybe<Scalars['String']['output']>;
  orderId: Scalars['String']['output'];
  partner?: Maybe<Partner>;
  patient?: Maybe<Profile>;
  pharmacyCode?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  total?: Maybe<Scalars['Float']['output']>;
  updatedAt: Scalars['DateTime']['output'];
  userLocation: Location;
};

export type HospitalMedicationPayload = {
  __typename?: 'HospitalMedicationPayload';
  errors: Array<ErrorPayload>;
  medication: HospitalMedication;
};

export type HospitalMedicationsConnection = {
  __typename?: 'HospitalMedicationsConnection';
  data: Array<HospitalMedication>;
  pageInfo: PageInfo;
};

export type IdInput = {
  id: Scalars['String']['input'];
};

export type IdPastIllnessType = {
  __typename?: 'IdPastIllnessType';
  id?: Maybe<PastIllnessType>;
};

export type Illness = {
  __typename?: 'Illness';
  _id: Scalars['ID']['output'];
  createdAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['DateTimeScalar']['output']>;
};

export type IllnessConnection = {
  __typename?: 'IllnessConnection';
  data: Array<Illness>;
  pageInfo: PageInfo;
};

export type IllnessInput = {
  id: Scalars['String']['input'];
};

export type IllnessOutput = {
  __typename?: 'IllnessOutput';
  _id: Scalars['ID']['output'];
  createdAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['DateTimeScalar']['output']>;
};

export type InitRtcInput = {
  consultationId: Scalars['String']['input'];
};

export type InitRtcPayload = {
  __typename?: 'InitRtcPayload';
  rtcToken: Scalars['String']['output'];
  uid: Scalars['Int']['output'];
};

export type InitializeDoctorInput = {
  doctors: Array<Scalars['String']['input']>;
};

export type IssueConnection = {
  __typename?: 'IssueConnection';
  issue: Symptoms;
};

export type IssueObj = {
  __typename?: 'IssueObj';
  Accuracy?: Maybe<Scalars['String']['output']>;
  ID?: Maybe<Scalars['String']['output']>;
  Icd?: Maybe<Scalars['String']['output']>;
  IcdName?: Maybe<Scalars['String']['output']>;
  Name?: Maybe<Scalars['String']['output']>;
  ProfName?: Maybe<Scalars['String']['output']>;
  Ranking?: Maybe<Scalars['String']['output']>;
};

export type IssuesConnection = {
  __typename?: 'IssuesConnection';
  issues: Array<Symptoms>;
};

export type Lab = {
  __typename?: 'Lab';
  _id?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<DoctorProfile>;
  partner?: Maybe<Partner>;
  patient?: Maybe<Profile>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type LabArrayPayload = {
  __typename?: 'LabArrayPayload';
  data?: Maybe<Array<Lab>>;
  errors?: Maybe<Array<ErrorPayload>>;
  message?: Maybe<Scalars['String']['output']>;
  pageInfo?: Maybe<PageInfo>;
};

export type LabInfo = {
  __typename?: 'LabInfo';
  name?: Maybe<Scalars['String']['output']>;
};

export type LabInfoInput = {
  name: Scalars['String']['input'];
};

export type LabResultPayload = {
  __typename?: 'LabResultPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  lab?: Maybe<Lab>;
  message?: Maybe<Scalars['String']['output']>;
};

export type License = {
  expiry_date: Scalars['DateTime']['input'];
  image: Scalars['String']['input'];
  number: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type Location = {
  __typename?: 'Location';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  landmark?: Maybe<Scalars['String']['output']>;
  lat?: Maybe<Scalars['Float']['output']>;
  lga?: Maybe<Scalars['String']['output']>;
  lng?: Maybe<Scalars['Float']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
};

export type LoginPayload = {
  __typename?: 'LoginPayload';
  account: Account;
  message: Scalars['String']['output'];
};

export type LoginSocialDto = {
  authType: Scalars['String']['input'];
  token: Scalars['String']['input'];
};

export type LoginSocialInput = {
  authType: Scalars['String']['input'];
  deviceId: Scalars['String']['input'];
  email: Scalars['String']['input'];
};

export type LoginUserInput = {
  authType: Scalars['String']['input'];
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type MarkNotificationReadInput = {
  id: Scalars['String']['input'];
  user: Scalars['String']['input'];
};

export type MedicationsArrayPayload = {
  __typename?: 'MedicationsArrayPayload';
  medication?: Maybe<Array<PatientMedication>>;
  message?: Maybe<Scalars['String']['output']>;
  pageInfo?: Maybe<PageInfo>;
};

export type MedicationsPayload = {
  __typename?: 'MedicationsPayload';
  medication?: Maybe<PatientMedication>;
  message?: Maybe<Scalars['String']['output']>;
};

export type Message = {
  __typename?: 'Message';
  _id?: Maybe<Scalars['String']['output']>;
  body?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  recipient?: Maybe<Profile>;
  sender?: Maybe<Scalars['String']['output']>;
  subject?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type MessageConnection = {
  __typename?: 'MessageConnection';
  messages: Array<Message>;
  pageInfo: PageInfo;
};

export type MessagePayload = {
  __typename?: 'MessagePayload';
  messages?: Maybe<Message>;
};

export type MessagingConsultationInput = {
  consultationId: Scalars['String']['input'];
};

export type MessagingInput = {
  id: Scalars['String']['input'];
};

export type MultipleDoctorProfileArrayPayload = {
  __typename?: 'MultipleDoctorProfileArrayPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  profiles?: Maybe<Array<DoctorProfile>>;
};

export type MultipleDoctorProfileInput = {
  ids: Array<Scalars['String']['input']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  acceptConsultation: ConsultationPayload;
  acceptInvite: Employee;
  acceptRenewal: Verification;
  accountsNeverConsulted: Scalars['Boolean']['output'];
  addBankAccount: DoctorProfilePayload;
  addDependants: EmployeeDependants;
  addDiagnosticLabTest: DiagnosticLabTestPayload;
  addDiagnosticReferral: DiagnosticTestPayload;
  addDoctorConsultation: ConsultationPayload;
  addDrugOrder: DrugOrderPayload;
  addHospitalMedication: HospitalMedicationPayload;
  addPartner: PartnerPayload;
  addPartnerCategory: PartnerCategoryPayload;
  addPastIllness: CreateProfile;
  addPrescriptions: Prescription;
  addSpecialization: Specialization;
  bulkUpdateReferralDrugs: PrescriptionReferral;
  bulkUpdateReferralTests: PrescriptionReferral;
  cancelConsultation: ConsultationPayload;
  cancelDiagnosticTest: DiagnosticTestPayload;
  cancelDispute: ConsultationPayload;
  cancelDrugOrder: DrugOrderPayload;
  changeDrugApprovalStatus: DrugsApprovals;
  changeTestApprovalStatus: TestsApprovals;
  completeDiagnosticTest: DiagnosticTestPayload;
  completePasswordReset: AccountPayload;
  confirmDoctorPatientInvite: DoctorPatientPayload;
  confirmEmployeeInvite: Employee;
  confirmInvite: FamilyPayload;
  createAllergy: AllergyPayload;
  createAppointment: AppointmentPayload;
  createAvailabileTime: AvailableTimePayload;
  createBusiness: CreateBusinessPayload;
  createCompany: CompanyPayload;
  createConsultation: ConsultationPayload;
  createCurrentIllness: CreateCurrentIllness;
  createDispute: ConsultationPayload;
  createDoctorAccount: DoctorProfilePayload;
  createDoctorProfile: DoctorProfilePayload;
  createEarning: EarningPayload;
  createEnrollee: EnrolleePayload;
  createEnterprise: CreateBusinessPayload;
  createExternalPlan: ExternalPlan;
  createIllness: IllnessOutput;
  createLabResult: LabResultPayload;
  createMedication: MedicationsPayload;
  createMessage: MessagePayload;
  createPatientAccount: CreateProfile;
  createPayout: PayoutPayload;
  createPermission: PermissionPayload;
  createPlan: PopulatedPlan;
  createPrescriptionReferral: PrescriptionReferral;
  createPriority: DoctorThreshold;
  createProfile: CreateProfile;
  createProvider: ProviderPayload;
  createQuote: Quote;
  createReminder: ReminderPayload;
  createRequest: RequestPayload;
  createRole: Role;
  createSetting: SettingPayload;
  createTicket: TicketPayload;
  createUserType: UserTypePayload;
  createVerification: VerificationPayload;
  deactivateCompanyEnrollees: EnrolleeDeactivatedPayload;
  deactivateEnrollee: EnrolleePayload;
  declineConsultation: ConsultationPayload;
  deleteAccount: Scalars['Int']['output'];
  deleteAllergy: Scalars['Int']['output'];
  deleteAppointment: Scalars['Int']['output'];
  deleteAvailableTime: DeleteAvailableTimePayload;
  deleteCompany: Scalars['Int']['output'];
  deleteConsultation: DeleteConsultationPayload;
  deleteDoctorAvailability: Scalars['Int']['output'];
  deleteDoctorPatient: DeleteDoctorPatientPayload;
  deleteDoctorProfile: Scalars['Int']['output'];
  deleteEnrollee: Scalars['Int']['output'];
  deleteIllness: Scalars['Int']['output'];
  deleteLabResult: Scalars['Int']['output'];
  deleteMedication: Scalars['Int']['output'];
  deleteMember: Scalars['Int']['output'];
  deleteMyAccount: Scalars['Boolean']['output'];
  deletePermission: DeletePermissionPayload;
  deletePlan: Scalars['Int']['output'];
  deleteProfile: Scalars['Int']['output'];
  deleteProvider: DeleteProviderPayload;
  deleteReminder: Scalars['Int']['output'];
  deleteRole: Scalars['Int']['output'];
  deleteSetting: DeleteSettingPayload;
  deleteSpecialization: Scalars['Boolean']['output'];
  deleteUserType: Scalars['Int']['output'];
  disableAccount: Scalars['Boolean']['output'];
  editDependant: Employee;
  editEmployee: Employee;
  editMember: FamilyPayload;
  endCommunication: ConsultationPayload;
  endConsultation: ConsultationCombinedPayload;
  enrolleeDrugOrder: DrugOrderPayload;
  failConsultation: DeleteConsultationPayload;
  favouriteDoctor: FavouriteDoctorPayload;
  finalizeReferralDrugs: PrescriptionReferral;
  finalizeReferralSpecialist: PrescriptionReferral;
  finalizeReferralTests: PrescriptionReferral;
  fulfillDrugOrder: DrugOrderPayload;
  generateAccountApiKey: ApikeyPayload;
  generateEnrollee: Scalars['JSON']['output'];
  generateNemEnrollee: Scalars['JSON']['output'];
  getQuote: Quote;
  initPayment: PaymentInitPayload;
  initRtc: InitRtcPayload;
  initializeDoctor: PayoutsListConnection;
  initializePayout: PayoutsListConnection;
  inviteDoctorPatient: DoctorPatientPayload;
  inviteEmployee: Employee;
  inviteMember: FamilyPayload;
  login: LoginPayload;
  loginSocial: LoginPayload;
  logout: Scalars['Boolean']['output'];
  markMessagesRead: ChatMessagesPayload;
  markNotificationRead: Notification;
  markNotificationScheduleNotified: Scalars['JSON']['output'];
  overrideEnrollees: Scalars['Boolean']['output'];
  rateConsultation: ConsultationPayload;
  reactivateCompanyEnrollees: EnrolleeDeactivatedPayload;
  rebroadcastConsultationNotification: ConsultationPayload;
  refreshToken: RefreshPayload;
  regenerateProviderProfileUrl: PopulatedProviderPayload;
  registerBusiness: RegisterBusinessPayload;
  registerFamily: RegisterFamilyPayload;
  registerMicroInsuranceIndividual: RegisterMicroInsuranceIndividualPayload;
  rejectLicenseRenewal: RejectionVerificationPayload;
  rejectVerification: RejectionVerificationPayload;
  removeBusiness: Business;
  removeDependant: Scalars['Boolean']['output'];
  removeEmployee: Scalars['Boolean']['output'];
  renewLicense: VerificationPayload;
  requestReferral: ReferralPayload;
  rescheduleConsultation: ConsultationPayload;
  resendEnrolleeData: ConsultationCombinedPayload;
  resendInvite: Scalars['Boolean']['output'];
  resendOTP: Scalars['Boolean']['output'];
  resetPassword: Scalars['Boolean']['output'];
  resolveDispute: ConsultationPayload;
  restoreAccount: AccountData;
  scheduleDiagnosticTest: DiagnosticTestPayload;
  sendChatMessage: ChatMessagePayload;
  sendConsultation: ConsultationPayload;
  sendNotification: FcmNotificationPayload;
  sendOTP: Scalars['Boolean']['output'];
  setAvailability: AvailabilityPayload;
  setEmployeePermanentPassword: AccountPayload;
  setPermanentPassword: AccountPayload;
  signup: SignupPayload;
  socialLogin: LoginPayload;
  socialSignup: SignupPayload;
  startCommunication: ConsultationPayload;
  testDate: Scalars['JSON']['output'];
  unFavouriteDoctor: Scalars['Int']['output'];
  updateAllergy: AllergyPayload;
  updateAppointment: AppointmentPayload;
  updateAvailableTime: AvailableTimePayload;
  updateBusiness: Business;
  updateCompany: CompanyPayload;
  updateConsultation: ConsultationPayload;
  updateDiagnosticLabTest: DiagnosticLabTestPayload;
  updateDoctorPatient: DoctorPatientPayload;
  updateDoctorProfile: DoctorProfilePayload;
  updateDoctorProvider: UpdateDoctorProviderPayload;
  updateDrugOrder: DrugOrderPayload;
  updateEmail: AccountData;
  updateEmployee: Employee;
  updateEnrollee: EnrolleePayload;
  updateExternalPlan: ExternalPlan;
  updateFamily: FamilyPayload;
  updateFcmToken: FcmToken;
  updateHospitalMedication: HospitalMedicationPayload;
  updateIllness: IllnessOutput;
  updateJoinedConsultation: ConsultationPayload;
  updateLabResult: LabResultPayload;
  updateLicense: VerificationPayload;
  updateMedication: MedicationsPayload;
  updatePartnerConfiguration: PartnerConfiguration;
  updatePartnerProfile: PartnerPayload;
  updatePartnerSubdomain: PartnerConfiguration;
  updatePassword: AccountData;
  updatePermission: PermissionPayload;
  updatePharmacyDrug: UpdatePharmacyDrugPayload;
  updatePlan: PopulatedPlan;
  updatePlanOnPayStack: ExternalPlan;
  updatePrescriptionReferral: PrescriptionReferral;
  updatePriority: DoctorThreshold;
  updateProfile: CreateProfile;
  updateProvider: PopulatedProviderPayload;
  updateQuote: Quote;
  updateReferral: ReferralPayload;
  updateReferralDrug: PrescriptionReferral;
  updateReferralTest: PrescriptionReferral;
  updateReminder: ReminderPayload;
  updateRole: Role;
  updateSatisfaction: ConsultationPayload;
  updateSetting: SettingPayload;
  updateSpecialization: Specialization;
  updateUserProvider: AccountProfile;
  updateUserType: UserTypePayload;
  uploadChatMessages: ChatMessagesPayload;
  uploadDiagnosticLabTests: UploadLabTestsPayload;
  uploadEmployees: UploadEmployeesPayload;
  uploadEnrollees: UploadEnrolleesPayload;
  uploadPharmacyDrugs: UploadPharmacyDrugsPayload;
  validateEnrollee: EnrolleePayload;
  verifyEmail: VerifyEmailPayload;
  verifyHCP: Verification;
};


export type MutationAcceptConsultationArgs = {
  data: AcceptConsultationInput;
};


export type MutationAcceptRenewalArgs = {
  data: VerifyHcpInput;
};


export type MutationAddBankAccountArgs = {
  data: AddBankAccountInput;
};


export type MutationAddDependantsArgs = {
  data: AddDependants;
};


export type MutationAddDiagnosticLabTestArgs = {
  data: DiagnosticLabTestInput;
};


export type MutationAddDiagnosticReferralArgs = {
  data: AddDiagnosticReferralInput;
};


export type MutationAddDoctorConsultationArgs = {
  data: AddDoctorConsultationInput;
};


export type MutationAddDrugOrderArgs = {
  data: AddDrugOrderInput;
};


export type MutationAddHospitalMedicationArgs = {
  data: AddHospitalMedicationInput;
};


export type MutationAddPartnerArgs = {
  data: AddPartnerInput;
};


export type MutationAddPartnerCategoryArgs = {
  data: PartnerCategoryInput;
};


export type MutationAddPastIllnessArgs = {
  data: UpdatePatientIllnessInput;
};


export type MutationAddPrescriptionsArgs = {
  data: AddPrescriptionsInput;
};


export type MutationAddSpecializationArgs = {
  data: AddSpecializationInput;
};


export type MutationBulkUpdateReferralDrugsArgs = {
  data: BulkUpdateInput;
};


export type MutationBulkUpdateReferralTestsArgs = {
  data: BulkUpdateInput;
};


export type MutationCancelConsultationArgs = {
  data: CancelConsultationInput;
};


export type MutationCancelDiagnosticTestArgs = {
  data: CancelDiagnosticTestInput;
};


export type MutationCancelDisputeArgs = {
  data: CancelDisputeInput;
};


export type MutationCancelDrugOrderArgs = {
  data: CancelDrugOrderInput;
};


export type MutationChangeDrugApprovalStatusArgs = {
  data: ChangeApprovalStatus;
};


export type MutationChangeTestApprovalStatusArgs = {
  data: ChangeApprovalStatus;
};


export type MutationCompleteDiagnosticTestArgs = {
  data: CompleteDiagnosticTestInput;
};


export type MutationCompletePasswordResetArgs = {
  data: CompletePasswordInput;
};


export type MutationConfirmDoctorPatientInviteArgs = {
  data: ConfirmPatientInviteInput;
};


export type MutationConfirmEmployeeInviteArgs = {
  data: ConfirmEmployeeInput;
};


export type MutationConfirmInviteArgs = {
  data: ConfirmInviteInput;
};


export type MutationCreateAllergyArgs = {
  data: CreateAllergyInput;
};


export type MutationCreateAppointmentArgs = {
  data: CreateAppointmentInput;
};


export type MutationCreateAvailabileTimeArgs = {
  data: AvailableTimeInput;
};


export type MutationCreateBusinessArgs = {
  data: CreateBusinessInput;
};


export type MutationCreateCompanyArgs = {
  data: CreateCompanyInput;
};


export type MutationCreateConsultationArgs = {
  data: CreateConsultationInput;
};


export type MutationCreateCurrentIllnessArgs = {
  data: CreateCurrentIllnessInput;
};


export type MutationCreateDisputeArgs = {
  data: CreateDisputeInput;
};


export type MutationCreateDoctorAccountArgs = {
  data: CreateDoctorProfileAccountInput;
};


export type MutationCreateDoctorProfileArgs = {
  data: CreateDoctorProfileInput;
};


export type MutationCreateEarningArgs = {
  data: CreateEarningInput;
};


export type MutationCreateEnrolleeArgs = {
  data: CreateEnrolleeInput;
};


export type MutationCreateEnterpriseArgs = {
  data: CreateEnterpriseInput;
};


export type MutationCreateExternalPlanArgs = {
  data: CreateInsurancePlanInput;
};


export type MutationCreateIllnessArgs = {
  data: CreateIllnessInput;
};


export type MutationCreateLabResultArgs = {
  data: CreateLabResultInput;
};


export type MutationCreateMedicationArgs = {
  data: CreatePatientMedicationInput;
};


export type MutationCreateMessageArgs = {
  data: CreateMessageInput;
};


export type MutationCreatePatientAccountArgs = {
  data: CreatePatientProfileInput;
};


export type MutationCreatePayoutArgs = {
  data: CreatePayoutInput;
};


export type MutationCreatePermissionArgs = {
  data: CreatePermissionInput;
};


export type MutationCreatePlanArgs = {
  data: CreatePlanInput;
};


export type MutationCreatePrescriptionReferralArgs = {
  data: AddPrescriptionReferralInput;
};


export type MutationCreatePriorityArgs = {
  data: Priority;
};


export type MutationCreateProfileArgs = {
  data: CreateProfileInput;
};


export type MutationCreateProviderArgs = {
  data: CreateProviderInput;
};


export type MutationCreateQuoteArgs = {
  data: AddQuoteInput;
};


export type MutationCreateReminderArgs = {
  data: CreateReminderInput;
};


export type MutationCreateRequestArgs = {
  data: CreateRequestInput;
};


export type MutationCreateRoleArgs = {
  data: CreateRoleInput;
};


export type MutationCreateSettingArgs = {
  data: CreateSettingInput;
};


export type MutationCreateTicketArgs = {
  data: CreateTicketInput;
};


export type MutationCreateUserTypeArgs = {
  data: CreateUserTypeInput;
};


export type MutationCreateVerificationArgs = {
  data: CreateVerificationInput;
};


export type MutationDeactivateCompanyEnrolleesArgs = {
  data: DeactivateCompanyEnrolleeInput;
};


export type MutationDeactivateEnrolleeArgs = {
  data: DeactivateEnrolleeInput;
};


export type MutationDeclineConsultationArgs = {
  data: DeclineConsultationInput;
};


export type MutationDeleteAllergyArgs = {
  data: DeleteAllergyInput;
};


export type MutationDeleteAppointmentArgs = {
  data: AppointmentInput;
};


export type MutationDeleteAvailableTimeArgs = {
  data: AvailableIdInput;
};


export type MutationDeleteCompanyArgs = {
  data: CompanyInput;
};


export type MutationDeleteConsultationArgs = {
  data: ConsultationId;
};


export type MutationDeleteDoctorAvailabilityArgs = {
  data: AvailableIdInput;
};


export type MutationDeleteDoctorPatientArgs = {
  data: DoctorPatientIdInput;
};


export type MutationDeleteDoctorProfileArgs = {
  data: DeleteProfileInput;
};


export type MutationDeleteEnrolleeArgs = {
  data: DeleteEnrolleeInput;
};


export type MutationDeleteIllnessArgs = {
  data: IllnessInput;
};


export type MutationDeleteLabResultArgs = {
  data: DeleteLabInput;
};


export type MutationDeleteMedicationArgs = {
  data: PatientMedicationInput;
};


export type MutationDeleteMemberArgs = {
  data: DeleteFamilyInput;
};


export type MutationDeleteMyAccountArgs = {
  data: DeleteMyAccountInput;
};


export type MutationDeletePermissionArgs = {
  data: PermissionInput;
};


export type MutationDeletePlanArgs = {
  data: DeletePlanInput;
};


export type MutationDeleteProfileArgs = {
  data: DoctorProfileInput;
};


export type MutationDeleteProviderArgs = {
  data: DeleteProviderInput;
};


export type MutationDeleteReminderArgs = {
  data: ReminderInput;
};


export type MutationDeleteRoleArgs = {
  data: DeleteRoleInput;
};


export type MutationDeleteSettingArgs = {
  data: DeleteSettingInput;
};


export type MutationDeleteSpecializationArgs = {
  data: IdInput;
};


export type MutationDeleteUserTypeArgs = {
  data: DeleteUserTypeInput;
};


export type MutationDisableAccountArgs = {
  data: DisableAccountInput;
};


export type MutationEditDependantArgs = {
  data: RemoveDependantInput;
};


export type MutationEditEmployeeArgs = {
  data: EditEmployeeInput;
};


export type MutationEditMemberArgs = {
  data: CreateFamilyInput;
};


export type MutationEndCommunicationArgs = {
  data: EndConsultationInput;
};


export type MutationEndConsultationArgs = {
  data: ConsultationId;
};


export type MutationEnrolleeDrugOrderArgs = {
  data: EnrolleeOrderInput;
};


export type MutationFailConsultationArgs = {
  data: ConsultationId;
};


export type MutationFavouriteDoctorArgs = {
  data: FavouriteDoctorInput;
};


export type MutationFinalizeReferralDrugsArgs = {
  data: FinalizeInput;
};


export type MutationFinalizeReferralSpecialistArgs = {
  data: FinalizeSpecialistInput;
};


export type MutationFinalizeReferralTestsArgs = {
  data: FinalizeInput;
};


export type MutationFulfillDrugOrderArgs = {
  data: DrugOrderInput;
};


export type MutationGenerateEnrolleeArgs = {
  data: GenerateHmoInput;
};


export type MutationGenerateNemEnrolleeArgs = {
  data: GenerateHmoIdInput;
};


export type MutationGetQuoteArgs = {
  data: QuoteInput;
};


export type MutationInitPaymentArgs = {
  data: PaymentInitInput;
};


export type MutationInitRtcArgs = {
  data: InitRtcInput;
};


export type MutationInitializeDoctorArgs = {
  data: InitializeDoctorInput;
};


export type MutationInitializePayoutArgs = {
  data: CreatePayoutsInput;
};


export type MutationInviteDoctorPatientArgs = {
  data: CreateDoctorPatientInput;
};


export type MutationInviteEmployeeArgs = {
  data: AddEmployeeInput;
};


export type MutationInviteMemberArgs = {
  data: CreateFamilyInput;
};


export type MutationLoginArgs = {
  data: LoginUserInput;
};


export type MutationLoginSocialArgs = {
  data: LoginSocialDto;
};


export type MutationMarkMessagesReadArgs = {
  data: MessagingInput;
};


export type MutationMarkNotificationReadArgs = {
  data: MarkNotificationReadInput;
};


export type MutationMarkNotificationScheduleNotifiedArgs = {
  data: ConsultationId;
};


export type MutationOverrideEnrolleesArgs = {
  data: UploadEnrolleesInput;
};


export type MutationRateConsultationArgs = {
  data: AddRatingInput;
};


export type MutationReactivateCompanyEnrolleesArgs = {
  data: DeactivateCompanyEnrolleeInput;
};


export type MutationRebroadcastConsultationNotificationArgs = {
  data: RebroadCastNotificationInput;
};


export type MutationRegenerateProviderProfileUrlArgs = {
  data: UpdateRegenerateInput;
};


export type MutationRegisterBusinessArgs = {
  data: RegisterBusinessInput;
};


export type MutationRegisterFamilyArgs = {
  data: RegisterFamilyInput;
};


export type MutationRegisterMicroInsuranceIndividualArgs = {
  data: RegisterMicroInsuranceIndividualInput;
};


export type MutationRejectLicenseRenewalArgs = {
  data: RejectVerificationInput;
};


export type MutationRejectVerificationArgs = {
  data: RejectVerificationInput;
};


export type MutationRemoveBusinessArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemoveDependantArgs = {
  data: RemoveDependantInput;
};


export type MutationRemoveEmployeeArgs = {
  data: EmployeeInput;
};


export type MutationRenewLicenseArgs = {
  data: RenewLicenseInput;
};


export type MutationRequestReferralArgs = {
  data: RequestReferralInput;
};


export type MutationRescheduleConsultationArgs = {
  data: RescheduleConsultationInput;
};


export type MutationResendEnrolleeDataArgs = {
  data: ConsultationId;
};


export type MutationResendInviteArgs = {
  data: ResendEmployeeInput;
};


export type MutationResendOtpArgs = {
  data: ResendInput;
};


export type MutationResetPasswordArgs = {
  data: ResetInput;
};


export type MutationResolveDisputeArgs = {
  data: ResolveDisputeInput;
};


export type MutationRestoreAccountArgs = {
  data: RestoreMyAccountInput;
};


export type MutationScheduleDiagnosticTestArgs = {
  data: AddDiagnosticTestInput;
};


export type MutationSendChatMessageArgs = {
  data: ChatMessageInput;
};


export type MutationSendConsultationArgs = {
  data: ConsultationId;
};


export type MutationSendNotificationArgs = {
  data: SendNotificationsInput;
};


export type MutationSendOtpArgs = {
  data: ResendInput;
};


export type MutationSetAvailabilityArgs = {
  data: SetAvailabilityInput;
};


export type MutationSetEmployeePermanentPasswordArgs = {
  data: SetPermanentPassword;
};


export type MutationSetPermanentPasswordArgs = {
  data: SetPermanentPassword;
};


export type MutationSignupArgs = {
  data: SignupUserInput;
};


export type MutationSocialLoginArgs = {
  data: LoginSocialInput;
};


export type MutationSocialSignupArgs = {
  data: SocialSignupUserInput;
};


export type MutationStartCommunicationArgs = {
  data: ConsultationId;
};


export type MutationUnFavouriteDoctorArgs = {
  data: FavouriteDoctorInput;
};


export type MutationUpdateAllergyArgs = {
  data: UpdateAllergyInput;
};


export type MutationUpdateAppointmentArgs = {
  data: UpdateAppointmentInput;
};


export type MutationUpdateAvailableTimeArgs = {
  data: UpdateAvailableTimeInput;
};


export type MutationUpdateBusinessArgs = {
  updateBusinessInput: UpdateBusinessInput;
};


export type MutationUpdateCompanyArgs = {
  data: UpdateCompanyInput;
};


export type MutationUpdateConsultationArgs = {
  data: UpdateConsultationInput;
};


export type MutationUpdateDiagnosticLabTestArgs = {
  data: UpdateDiagnosticLabTestInput;
};


export type MutationUpdateDoctorPatientArgs = {
  data: UpdateDoctorPatient;
};


export type MutationUpdateDoctorProfileArgs = {
  data: UpdateDoctorProfileInput;
};


export type MutationUpdateDoctorProviderArgs = {
  data: UpdateDoctorProviderInput;
};


export type MutationUpdateDrugOrderArgs = {
  data: UpdateDrugOrderInput;
};


export type MutationUpdateEmailArgs = {
  data: UpdateEmailInput;
};


export type MutationUpdateEmployeeArgs = {
  data: UpdateEmployeeInput;
};


export type MutationUpdateEnrolleeArgs = {
  data: UpdateEnrolleeInput;
};


export type MutationUpdateExternalPlanArgs = {
  data: UpdateInsurancePlanInput;
};


export type MutationUpdateFamilyArgs = {
  data: UpdateFamilyInput;
};


export type MutationUpdateFcmTokenArgs = {
  data: UpdateFcmTokenInput;
};


export type MutationUpdateHospitalMedicationArgs = {
  data: UpdateHospitalMedicationInput;
};


export type MutationUpdateIllnessArgs = {
  data: UpdateIllnessInput;
};


export type MutationUpdateJoinedConsultationArgs = {
  data: UpdateJoinedInput;
};


export type MutationUpdateLabResultArgs = {
  data: UpdateLabInput;
};


export type MutationUpdateLicenseArgs = {
  data: CreateVerificationInput;
};


export type MutationUpdateMedicationArgs = {
  data: UpdatePatientMedicationInput;
};


export type MutationUpdatePartnerConfigurationArgs = {
  data: PartnerConfigurationInput;
};


export type MutationUpdatePartnerProfileArgs = {
  data: UpdatePartnerInput;
};


export type MutationUpdatePartnerSubdomainArgs = {
  data: PartnerSubdomainInput;
};


export type MutationUpdatePasswordArgs = {
  data: UpdatePasswordInput;
};


export type MutationUpdatePermissionArgs = {
  data: UpdatePermissionInput;
};


export type MutationUpdatePharmacyDrugArgs = {
  data: UpdatePharmacyDrugInput;
};


export type MutationUpdatePlanArgs = {
  data: UpdatePlanInput;
};


export type MutationUpdatePlanOnPayStackArgs = {
  data: UpdatePayStackPlanInput;
};


export type MutationUpdatePrescriptionReferralArgs = {
  data: UpdatePrescriptionReferralInput;
};


export type MutationUpdatePriorityArgs = {
  data: UpdatePriority;
};


export type MutationUpdateProfileArgs = {
  data: UpdateProfileInput;
};


export type MutationUpdateProviderArgs = {
  data: UpdateProviderInput;
};


export type MutationUpdateQuoteArgs = {
  data: QuoteStatusInput;
};


export type MutationUpdateReferralArgs = {
  data: UpdateReferralInput;
};


export type MutationUpdateReferralDrugArgs = {
  data: UpdateDrugInput;
};


export type MutationUpdateReferralTestArgs = {
  data: UpdateTestInput;
};


export type MutationUpdateReminderArgs = {
  data: UpdateReminderInput;
};


export type MutationUpdateRoleArgs = {
  data: UpdateRoleInput;
};


export type MutationUpdateSatisfactionArgs = {
  data: UpdateSatisFactionInput;
};


export type MutationUpdateSettingArgs = {
  data: UpdateSettingInput;
};


export type MutationUpdateSpecializationArgs = {
  data: UpdateSpecializationInput;
};


export type MutationUpdateUserProviderArgs = {
  data: UpdateUserProviderInput;
};


export type MutationUpdateUserTypeArgs = {
  data: UpdateUserTypeInput;
};


export type MutationUploadChatMessagesArgs = {
  data: ChatMessagesUploadInput;
};


export type MutationUploadDiagnosticLabTestsArgs = {
  data: UploadLabTestsInput;
};


export type MutationUploadEmployeesArgs = {
  data: UploadEmployeesInput;
};


export type MutationUploadEnrolleesArgs = {
  data: UploadEnrolleesInput;
};


export type MutationUploadPharmacyDrugsArgs = {
  data: UploadPharmacyDrugsInput;
};


export type MutationValidateEnrolleeArgs = {
  data: ValidateEnrolleeInput;
};


export type MutationVerifyEmailArgs = {
  data: VerifyEmailInput;
};


export type MutationVerifyHcpArgs = {
  data: VerifyHcpInput;
};

export type NewsLetterContentInput = {
  content?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type NewsLetterInput = {
  body: Array<NewsLetterContentInput>;
  heading: Scalars['String']['input'];
  to: Array<Scalars['String']['input']>;
};

export type Notification = {
  __typename?: 'Notification';
  _id?: Maybe<Scalars['ID']['output']>;
  content?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  itemId?: Maybe<Scalars['String']['output']>;
  previewImageUri?: Maybe<Scalars['String']['output']>;
  previewImageUriThumbnail?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Scalars['String']['output']>;
  saveNotification?: Maybe<Scalars['Boolean']['output']>;
  seen?: Maybe<Scalars['Boolean']['output']>;
  tag?: Maybe<Scalars['String']['output']>;
  ticker?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  useSound?: Maybe<Scalars['String']['output']>;
  user?: Maybe<Scalars['String']['output']>;
};

export type NotificationSchedule = {
  __typename?: 'NotificationSchedule';
  _id?: Maybe<Scalars['ID']['output']>;
  consultation?: Maybe<Consultation>;
  consultationId?: Maybe<Scalars['String']['output']>;
  notified?: Maybe<Scalars['Boolean']['output']>;
  time?: Maybe<Scalars['String']['output']>;
};

export type NotificationsConnection = {
  __typename?: 'NotificationsConnection';
  data: Array<Notification>;
};

export type PageInfo = {
  __typename?: 'PageInfo';
  hasNextPage?: Maybe<Scalars['Boolean']['output']>;
  hasPrevPage?: Maybe<Scalars['Boolean']['output']>;
  limit?: Maybe<Scalars['Int']['output']>;
  nextPage?: Maybe<Scalars['Int']['output']>;
  offset?: Maybe<Scalars['Int']['output']>;
  page?: Maybe<Scalars['Int']['output']>;
  pagingCounter?: Maybe<Scalars['Int']['output']>;
  prevPage?: Maybe<Scalars['Int']['output']>;
  totalDocs?: Maybe<Scalars['Int']['output']>;
  totalPages?: Maybe<Scalars['Int']['output']>;
};

export type Partner = {
  __typename?: 'Partner';
  _id: Scalars['ID']['output'];
  accountId?: Maybe<Scalars['String']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  bankDetails?: Maybe<Array<BankDetails>>;
  category?: Maybe<Scalars['String']['output']>;
  classification?: Maybe<Scalars['String']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  logoImageUrl?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  profileUrl?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  specialisation?: Maybe<Scalars['String']['output']>;
};

export type PartnerCategoriesConnection = {
  __typename?: 'PartnerCategoriesConnection';
  data: Array<PartnerCategory>;
};

export type PartnerCategory = {
  __typename?: 'PartnerCategory';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type PartnerCategoryInput = {
  name: Scalars['String']['input'];
};

export type PartnerCategoryPayload = {
  __typename?: 'PartnerCategoryPayload';
  category: PartnerCategory;
  errors: Array<ErrorPayload>;
};

export type PartnerConfiguration = {
  __typename?: 'PartnerConfiguration';
  apiKey?: Maybe<Scalars['String']['output']>;
  category?: Maybe<Scalars['String']['output']>;
  drugDeliveryFee?: Maybe<Scalars['Float']['output']>;
  drugPriceMarkUp?: Maybe<Scalars['Float']['output']>;
  partner?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  subdomain?: Maybe<Scalars['String']['output']>;
  testPickUpFee?: Maybe<Scalars['Float']['output']>;
  testPickUpFeeLimit?: Maybe<Scalars['Float']['output']>;
  testPriceMarkup?: Maybe<Scalars['Float']['output']>;
  widgetColor?: Maybe<Scalars['String']['output']>;
  widgetIcon?: Maybe<Scalars['String']['output']>;
  widgetLogo?: Maybe<Scalars['String']['output']>;
  widgetPosition?: Maybe<Scalars['String']['output']>;
  widgetSize?: Maybe<Scalars['String']['output']>;
  widgetTextColor?: Maybe<Scalars['String']['output']>;
  widgetTextHeader?: Maybe<Scalars['String']['output']>;
};

export type PartnerConfigurationInput = {
  category?: InputMaybe<Scalars['String']['input']>;
  drugDeliveryFee?: InputMaybe<Scalars['Float']['input']>;
  drugPriceMarkUp?: InputMaybe<Scalars['Float']['input']>;
  partner: Scalars['String']['input'];
  subdomain?: InputMaybe<Scalars['String']['input']>;
  testPickUpFee?: InputMaybe<Scalars['Float']['input']>;
  testPickUpFeeLimit?: InputMaybe<Scalars['Float']['input']>;
  testPriceMarkup?: InputMaybe<Scalars['Float']['input']>;
  widgetColor?: InputMaybe<Scalars['String']['input']>;
  widgetIcon?: InputMaybe<Scalars['String']['input']>;
  widgetLogo?: InputMaybe<Scalars['String']['input']>;
  widgetPosition?: InputMaybe<Scalars['String']['input']>;
  widgetSize?: InputMaybe<Scalars['String']['input']>;
  widgetTextColor?: InputMaybe<Scalars['String']['input']>;
  widgetTextHeader?: InputMaybe<Scalars['String']['input']>;
};

export type PartnerInput = {
  id: Scalars['String']['input'];
};

export type PartnerPayload = {
  __typename?: 'PartnerPayload';
  errors: Array<ErrorPayload>;
  partner: Partner;
};

export type PartnerSubdomainInput = {
  partner: Scalars['String']['input'];
  subdomain: Scalars['String']['input'];
};

export type PartnersConnection = {
  __typename?: 'PartnersConnection';
  data: Array<Partner>;
  pageInfo: PageInfo;
};

export type PastIllnessType = {
  __typename?: 'PastIllnessType';
  _id?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type PastIllnessTypes = {
  __typename?: 'PastIllnessTypes';
  description?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type PatientMedication = {
  __typename?: 'PatientMedication';
  _id?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor: Scalars['ID']['output'];
  dosage: Scalars['Float']['output'];
  interval?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  patient: Scalars['ID']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type PatientMedicationInput = {
  id: Scalars['String']['input'];
};

export type PatientProfile = {
  __typename?: 'PatientProfile';
  _id?: Maybe<Scalars['String']['output']>;
  accountId?: Maybe<Scalars['String']['output']>;
  bloodGroup?: Maybe<Scalars['String']['output']>;
  consultations?: Maybe<Scalars['Float']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  dob?: Maybe<Scalars['DateTimeScalar']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  genotype?: Maybe<Scalars['String']['output']>;
  height?: Maybe<Scalars['Float']['output']>;
  hmoId?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  pastIllness?: Maybe<Array<PastIllnessTypes>>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  plan?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Float']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  timezoneOffset?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  weight?: Maybe<Scalars['Float']['output']>;
};

export type PaymentInitInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  callback_url?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  itemId: Scalars['String']['input'];
  noOfUsers?: InputMaybe<Scalars['Float']['input']>;
  plan?: InputMaybe<Scalars['String']['input']>;
  reason: Scalars['String']['input'];
  saveCard?: InputMaybe<Scalars['Boolean']['input']>;
  scope?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  user: Scalars['String']['input'];
};

export type PaymentInitPayload = {
  __typename?: 'PaymentInitPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  paymentInitResponse: PaymentInitResponse;
};

export type PaymentInitResponse = {
  __typename?: 'PaymentInitResponse';
  authorization_url: Scalars['String']['output'];
  reference: Scalars['String']['output'];
};

export type Payout = {
  __typename?: 'Payout';
  _id: Scalars['ID']['output'];
  amount?: Maybe<Scalars['Float']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<DoctorProfile>;
  providerId?: Maybe<Provider>;
  status?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type PayoutConnection = {
  __typename?: 'PayoutConnection';
  data: Array<Payout>;
  errors?: Maybe<Array<ErrorPayload>>;
  pageInfo?: Maybe<PageInfo>;
};

export type PayoutPayload = {
  __typename?: 'PayoutPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  payout?: Maybe<Payout>;
};

export type PayoutsListConnection = {
  __typename?: 'PayoutsListConnection';
  data?: Maybe<Array<Payout>>;
};

export type PaystackAuthorization = {
  __typename?: 'PaystackAuthorization';
  account_name?: Maybe<Scalars['String']['output']>;
  authorization_code?: Maybe<Scalars['String']['output']>;
  bank?: Maybe<Scalars['String']['output']>;
  bin?: Maybe<Scalars['String']['output']>;
  brand?: Maybe<Scalars['String']['output']>;
  card_type?: Maybe<Scalars['String']['output']>;
  channel?: Maybe<Scalars['String']['output']>;
  country_code?: Maybe<Scalars['String']['output']>;
  exp_month?: Maybe<Scalars['String']['output']>;
  exp_year?: Maybe<Scalars['String']['output']>;
  last4?: Maybe<Scalars['String']['output']>;
  reusable?: Maybe<Scalars['String']['output']>;
  signature?: Maybe<Scalars['String']['output']>;
};

export type Permission = {
  __typename?: 'Permission';
  _id?: Maybe<Scalars['ID']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type PermissionConnection = {
  __typename?: 'PermissionConnection';
  errors?: Maybe<Array<ErrorPayload>>;
  pageInfo: PageInfo;
  permission: Array<Permission>;
};

export type PermissionInput = {
  id: Scalars['String']['input'];
};

export type PermissionPayload = {
  __typename?: 'PermissionPayload';
  errors: Array<ErrorPayload>;
  permission?: Maybe<Permission>;
};

export type PharmacyDashboard = {
  __typename?: 'PharmacyDashboard';
  cancelledDrugOrdersCount?: Maybe<Scalars['Float']['output']>;
  cancelledDrugOrdersStats?: Maybe<Array<DiagnosticCalendarPayload>>;
  completedDrugOrdersCount?: Maybe<Scalars['Float']['output']>;
  completedDrugOrdersStats?: Maybe<Array<DiagnosticCalendarPayload>>;
  drugOrderRequestsCount?: Maybe<Scalars['Float']['output']>;
  drugOrderRequestsStats?: Maybe<Array<DiagnosticCalendarPayload>>;
  processingDrugOrdersCount?: Maybe<Scalars['Float']['output']>;
  processingDrugOrdersStats?: Maybe<Array<DiagnosticCalendarPayload>>;
};

export type PharmacyDrug = {
  __typename?: 'PharmacyDrug';
  _id?: Maybe<Scalars['ID']['output']>;
  drugName?: Maybe<Scalars['String']['output']>;
  drugPrice?: Maybe<Scalars['Float']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Scalars['String']['output']>;
  priceListId?: Maybe<Scalars['Float']['output']>;
  quantity?: Maybe<Scalars['String']['output']>;
  unitPrice?: Maybe<Scalars['Float']['output']>;
};

export type PharmacyDrugsConnection = {
  __typename?: 'PharmacyDrugsConnection';
  data: Array<PharmacyDrug>;
};

export type Plan = {
  __typename?: 'Plan';
  _id?: Maybe<Scalars['ID']['output']>;
  allowedFeatures?: Maybe<Scalars['JSONObject']['output']>;
  amount?: Maybe<Scalars['Float']['output']>;
  createdAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  duration?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  provider?: Maybe<Scalars['String']['output']>;
  providerData?: Maybe<Scalars['JSONObject']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscribed?: Maybe<Scalars['Boolean']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTimeScalar']['output']>;
};

export type PlanCodeInput = {
  planCode: Scalars['String']['input'];
};

export type PlanPayload = {
  __typename?: 'PlanPayload';
  _id?: Maybe<Scalars['String']['output']>;
  allowedFeatures?: Maybe<AllowedFeatures>;
  amount?: Maybe<Scalars['Float']['output']>;
  benefits?: Maybe<Array<Benefit>>;
  billingDayOffset?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  duration?: Maybe<Scalars['String']['output']>;
  external?: Maybe<Scalars['Boolean']['output']>;
  externalProviderName?: Maybe<Scalars['Boolean']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  monthlyAmount?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  noOfDoctors?: Maybe<Scalars['Float']['output']>;
  planCode?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Provider>;
  specialisation?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscribed?: Maybe<Scalars['Boolean']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
  yearlyAmount?: Maybe<Scalars['Float']['output']>;
};

export type PlanType = {
  __typename?: 'PlanType';
  _id?: Maybe<Scalars['ID']['output']>;
  allowedFeatures?: Maybe<ConsultType>;
  amount?: Maybe<Scalars['Float']['output']>;
  billingDayOffset?: Maybe<Scalars['Int']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  duration?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  planCode?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type PlansPayload = {
  __typename?: 'PlansPayload';
  data?: Maybe<Array<PlanPayload>>;
  pageInfo?: Maybe<PageInfo>;
};

export type PopulatedAccount = {
  __typename?: 'PopulatedAccount';
  _id?: Maybe<Scalars['ID']['output']>;
  access_token?: Maybe<Scalars['String']['output']>;
  apiKey?: Maybe<Scalars['String']['output']>;
  authType?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deactivateType?: Maybe<Scalars['String']['output']>;
  deactivated?: Maybe<Scalars['Boolean']['output']>;
  deactivatedAt?: Maybe<Scalars['String']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeNumber?: Maybe<Scalars['String']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  isEmailVerified?: Maybe<Scalars['Boolean']['output']>;
  isPasswordTemporary?: Maybe<Scalars['Boolean']['output']>;
  nextStep?: Maybe<Scalars['String']['output']>;
  otpTokenExpiresAt?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['ID']['output']>;
  refresh_token?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userTypeId?: Maybe<UserAccountType>;
};

export type PopulatedBusiness = {
  __typename?: 'PopulatedBusiness';
  _id?: Maybe<Scalars['String']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  category?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  industry?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  noOfEmployees?: Maybe<Scalars['Float']['output']>;
  profileId?: Maybe<Profile>;
  providerId?: Maybe<ProviderType>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userTypeId?: Maybe<UserAccountType>;
};

export type PopulatedBusinessPayload = {
  __typename?: 'PopulatedBusinessPayload';
  data?: Maybe<Array<PopulatedBusiness>>;
  pageInfo?: Maybe<PageInfo>;
};

export type PopulatedDrugsApprovals = {
  __typename?: 'PopulatedDrugsApprovals';
  _id?: Maybe<Scalars['String']['output']>;
  consultation?: Maybe<UnPopulatedConsultation>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<UnPopulatedDoctorProfile>;
  drugs?: Maybe<Array<Drug>>;
  note?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Partner>;
  patient?: Maybe<Profile>;
  providerId?: Maybe<ProviderType>;
  referral?: Maybe<PrescriptionReferral>;
  status?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type PopulatedEmployeePayload = {
  __typename?: 'PopulatedEmployeePayload';
  data?: Maybe<Array<Employee>>;
  pageInfo?: Maybe<PageInfo>;
};

export type PopulatedPlan = {
  __typename?: 'PopulatedPlan';
  _id: Scalars['String']['output'];
  allowedFeatures?: Maybe<AllowedFeatures>;
  amount: Scalars['Float']['output'];
  billingDayOffset?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  duration?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  noOfDoctors?: Maybe<Scalars['Float']['output']>;
  planCode?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  specialisation?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type PopulatedPrescriptionReferral = {
  __typename?: 'PopulatedPrescriptionReferral';
  _id?: Maybe<Scalars['String']['output']>;
  consultation?: Maybe<UnPopulatedConsultation>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<UnPopulatedDoctorProfile>;
  drugStatus?: Maybe<Scalars['String']['output']>;
  drugs?: Maybe<Array<Drug>>;
  drugsNote?: Maybe<Scalars['String']['output']>;
  drugsPartner?: Maybe<Partner>;
  patient?: Maybe<Profile>;
  providerId?: Maybe<ProviderType>;
  specialistStatus?: Maybe<Scalars['String']['output']>;
  specialists?: Maybe<Array<Specialist>>;
  testStatus?: Maybe<Scalars['String']['output']>;
  tests?: Maybe<Array<Test>>;
  testsNote?: Maybe<Scalars['String']['output']>;
  testsPartner?: Maybe<Partner>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type PopulatedProfile = {
  __typename?: 'PopulatedProfile';
  _id?: Maybe<Scalars['ID']['output']>;
  accountId?: Maybe<Account>;
  address?: Maybe<Scalars['String']['output']>;
  bloodGroup?: Maybe<Scalars['String']['output']>;
  consultations?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  dob?: Maybe<Scalars['DateTime']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  externalPlanCode?: Maybe<Scalars['String']['output']>;
  externalPlanType?: Maybe<Scalars['String']['output']>;
  externalProvider?: Maybe<Scalars['String']['output']>;
  externalProviderId?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  genotype?: Maybe<Scalars['String']['output']>;
  height?: Maybe<Scalars['Float']['output']>;
  hmoId?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  isNemEnrollee?: Maybe<Scalars['Boolean']['output']>;
  isPrincipal?: Maybe<Scalars['Boolean']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  noOfDependant?: Maybe<Scalars['Float']['output']>;
  pastIllness?: Maybe<Array<IdPastIllnessType>>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  plan?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<ProviderType>;
  rating?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscriptionId?: Maybe<SubscriptionType>;
  timezoneOffset?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  weight?: Maybe<Scalars['Float']['output']>;
};

export type PopulatedProvider = {
  __typename?: 'PopulatedProvider';
  _id?: Maybe<Scalars['ID']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  doctorCount?: Maybe<Scalars['Int']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeCount?: Maybe<Scalars['Int']['output']>;
  hmoPlans?: Maybe<Array<HmoPlans>>;
  icon?: Maybe<Scalars['String']['output']>;
  iconAlt?: Maybe<Scalars['String']['output']>;
  isWellaHealthIntegration?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
  partnerCount?: Maybe<Scalars['Int']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  profileUrl?: Maybe<Scalars['String']['output']>;
  rareCase?: Maybe<Scalars['Boolean']['output']>;
  updatedAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  userCount?: Maybe<Scalars['Int']['output']>;
  userTypeId?: Maybe<UserType>;
};

export type PopulatedProviderPayload = {
  __typename?: 'PopulatedProviderPayload';
  message?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<PopulatedProvider>;
};

export type PopulatedTestsApprovals = {
  __typename?: 'PopulatedTestsApprovals';
  _id?: Maybe<Scalars['String']['output']>;
  consultation?: Maybe<UnPopulatedConsultation>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<UnPopulatedDoctorProfile>;
  note?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Partner>;
  patient?: Maybe<Profile>;
  providerId?: Maybe<ProviderType>;
  referral?: Maybe<PrescriptionReferral>;
  status?: Maybe<Scalars['String']['output']>;
  tests?: Maybe<Array<Test>>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type Prescription = {
  __typename?: 'Prescription';
  _id: Scalars['ID']['output'];
  consultation?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<DoctorProfile>;
  drugs?: Maybe<Array<PrescriptionDrug>>;
  patient?: Maybe<Profile>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type PrescriptionDrug = {
  __typename?: 'PrescriptionDrug';
  amount?: Maybe<Scalars['Float']['output']>;
  dosageFrequency?: Maybe<DosageFreq>;
  dosageQuantity?: Maybe<Scalars['String']['output']>;
  dosageUnit?: Maybe<Scalars['String']['output']>;
  drugForm?: Maybe<Scalars['String']['output']>;
  drugName?: Maybe<Scalars['String']['output']>;
  drugPrice?: Maybe<Scalars['Float']['output']>;
  instructions?: Maybe<Scalars['String']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  paid?: Maybe<Scalars['Boolean']['output']>;
  priceListId?: Maybe<Scalars['Float']['output']>;
  quantity?: Maybe<Scalars['Float']['output']>;
  route?: Maybe<Scalars['String']['output']>;
  unitPrice?: Maybe<Scalars['Float']['output']>;
};

export type PrescriptionInput = {
  amount: Scalars['Float']['input'];
  dosageFrequency: DosageFrequency;
  dosageUnit: Scalars['String']['input'];
  drugForm: Scalars['String']['input'];
  drugName: Scalars['String']['input'];
  drugPrice: Scalars['Float']['input'];
  instructions?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  paid?: InputMaybe<Scalars['Boolean']['input']>;
  priceListId: Scalars['Float']['input'];
  quantity: Scalars['Float']['input'];
  route: Scalars['String']['input'];
  unitPrice: Scalars['Float']['input'];
};

export type PrescriptionReferral = {
  __typename?: 'PrescriptionReferral';
  _id?: Maybe<Scalars['String']['output']>;
  consultation?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<Scalars['String']['output']>;
  drugStatus?: Maybe<Scalars['String']['output']>;
  drugs?: Maybe<Array<Drug>>;
  drugsNote?: Maybe<Scalars['String']['output']>;
  drugsPartner?: Maybe<Scalars['String']['output']>;
  patient?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  specialistStatus?: Maybe<Scalars['String']['output']>;
  specialists?: Maybe<Array<Specialist>>;
  testStatus?: Maybe<Scalars['String']['output']>;
  tests?: Maybe<Array<Test>>;
  testsNote?: Maybe<Scalars['String']['output']>;
  testsPartner?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type PrescriptionReferralInput = {
  id: Scalars['String']['input'];
};

export type PrescriptionReferralPayload = {
  __typename?: 'PrescriptionReferralPayload';
  data?: Maybe<Array<PopulatedPrescriptionReferral>>;
  pageInfo?: Maybe<PageInfo>;
};

export type PrescriptionsConnection = {
  __typename?: 'PrescriptionsConnection';
  data?: Maybe<Array<Prescription>>;
  pageInfo?: Maybe<PageInfo>;
};

export type Priority = {
  end?: InputMaybe<Scalars['Float']['input']>;
  priority: Scalars['Float']['input'];
  start: Scalars['Float']['input'];
};

export type Profile = {
  __typename?: 'Profile';
  _id?: Maybe<Scalars['ID']['output']>;
  accountData?: Maybe<Account>;
  bloodGroup?: Maybe<Scalars['String']['output']>;
  consultations?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  dob?: Maybe<Scalars['DateTime']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  externalPlanCode?: Maybe<Scalars['String']['output']>;
  externalPlanType?: Maybe<Scalars['String']['output']>;
  externalProvider?: Maybe<Scalars['String']['output']>;
  externalProviderId?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  genotype?: Maybe<Scalars['String']['output']>;
  height?: Maybe<Scalars['Float']['output']>;
  hmoId?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  noOfDependant?: Maybe<Scalars['Int']['output']>;
  pastIllness?: Maybe<Array<PastIllnessType>>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  plan?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  timezoneOffset?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  weight?: Maybe<Scalars['Float']['output']>;
};

export type ProfileConnection = {
  __typename?: 'ProfileConnection';
  data: Array<PopulatedProfile>;
  pageInfo: PageInfo;
};

export type Provider = {
  __typename?: 'Provider';
  _id?: Maybe<Scalars['ID']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  consultationLimitType?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctorCount?: Maybe<Scalars['Int']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeCount?: Maybe<Scalars['Int']['output']>;
  hmoPlans?: Maybe<Array<HmoPlans>>;
  icon?: Maybe<Scalars['String']['output']>;
  iconAlt?: Maybe<Scalars['String']['output']>;
  isWellaHealthIntegration?: Maybe<Scalars['Boolean']['output']>;
  monthlyConsultationLimit?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  partnerCount?: Maybe<Scalars['Int']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  profileUrl?: Maybe<Scalars['String']['output']>;
  rareCase?: Maybe<Scalars['Boolean']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userCount?: Maybe<Scalars['Int']['output']>;
  userTypeId?: Maybe<UserType>;
};

export type ProviderPayload = {
  __typename?: 'ProviderPayload';
  message?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Provider>;
};

export type ProviderPopulatedType = {
  __typename?: 'ProviderPopulatedType';
  _id?: Maybe<Scalars['ID']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  consultationLimitType?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctorCount?: Maybe<Scalars['Int']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeCount?: Maybe<Scalars['Int']['output']>;
  hmoPlans?: Maybe<Array<HmoPlans>>;
  icon?: Maybe<Scalars['String']['output']>;
  iconAlt?: Maybe<Scalars['String']['output']>;
  isWellaHealthIntegration?: Maybe<Scalars['Boolean']['output']>;
  monthlyConsultationLimit?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  partnerCount?: Maybe<Scalars['Int']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  profileUrl?: Maybe<Scalars['String']['output']>;
  rareCase?: Maybe<Scalars['Boolean']['output']>;
  totalPendingAmount?: Maybe<Scalars['Int']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userCount?: Maybe<Scalars['Int']['output']>;
  userTypeId?: Maybe<UserType>;
};

export type ProviderType = {
  __typename?: 'ProviderType';
  _id?: Maybe<Scalars['ID']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  hmoPlans?: Maybe<Array<HmoPlanType>>;
  icon?: Maybe<Scalars['String']['output']>;
  iconAlt?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  profileUrl?: Maybe<Scalars['String']['output']>;
  rareCase?: Maybe<Scalars['Boolean']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userTypeId?: Maybe<Scalars['String']['output']>;
};

export type ProviderTypeConnection = {
  __typename?: 'ProviderTypeConnection';
  pageInfo?: Maybe<PageInfo>;
  provider?: Maybe<Array<ProviderPopulatedType>>;
};

export type ProvidersType = {
  __typename?: 'ProvidersType';
  _id?: Maybe<Scalars['ID']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  consultationLimitType?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctorCount?: Maybe<Scalars['Int']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeCount?: Maybe<Scalars['Int']['output']>;
  hmoPlans?: Maybe<Array<HmoPlans>>;
  icon?: Maybe<Scalars['String']['output']>;
  iconAlt?: Maybe<Scalars['String']['output']>;
  isWellaHealthIntegration?: Maybe<Scalars['Boolean']['output']>;
  monthlyConsultationLimit?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  partnerCount?: Maybe<Scalars['Int']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  profileUrl?: Maybe<Scalars['String']['output']>;
  rareCase?: Maybe<Scalars['Boolean']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userCount?: Maybe<Scalars['Int']['output']>;
  userTypeId?: Maybe<UserType>;
};

export type Qualification = {
  degree: Scalars['String']['input'];
  image: Scalars['String']['input'];
  year: Scalars['DateTime']['input'];
};

export type Query = {
  __typename?: 'Query';
  PatientCount: Scalars['Int']['output'];
  accessConsultation: AccountPayload;
  accessDoctorConsultation: AccountPayload;
  account: Account;
  accountCount: Scalars['Int']['output'];
  accounts: AccountConnection;
  activityLogs: Array<ActivityLog>;
  allergies: AllergyArrayPayload;
  allergy: Allergy;
  business: Business;
  businesses: PopulatedBusinessPayload;
  cancelSubscription: CancelSubscriptionConnection;
  checkActivityLogger: Scalars['JSONObject']['output'];
  checkCharge: CheckChargeConnection;
  checkHasSubscription: CheckHasSubscriptionConnection;
  checkNemEnrollee: Scalars['JSON']['output'];
  countConsultation: Scalars['Int']['output'];
  debugSentry: Scalars['JSON']['output'];
  deleteDiagnosticLabTest: Scalars['Int']['output'];
  deletePartner: Partner;
  doctorCount: Scalars['Int']['output'];
  doctorProfile: DoctorProfilePayload;
  doctorProfileByEmail: DoctorProfilePayload;
  doctorProfiles: DoctorProfileArrayPayload;
  doctorProfilesByStatus: DoctorProfileArrayPayload;
  drugApproval: PopulatedDrugsApprovals;
  drugsApprovals: DrugsApprovalsPayload;
  employees: PopulatedEmployeePayload;
  exportConsultations: ExportDetails;
  exportConsultationsNew: Scalars['Boolean']['output'];
  families: FamilyArrayPayload;
  findMultipleDoctorProfiles: MultipleDoctorProfileArrayPayload;
  findMultipleProfiles: ProfileConnection;
  generateConsultationsStat: Scalars['JSON']['output'];
  getAllAppointments: AppointmentConnection;
  getAllStats: Stats;
  getAppointment: Appointment;
  getAppointments: AppointmentConnection;
  getAvailabilities: AvailabilityConnection;
  getAvailability: Availability;
  getAvailableDoctors: DoctorProfileArrayPayload;
  getAvailableTime: AvailableTime;
  getAvailableTimes: AvailableTimeConnection;
  getBookedConsultationTimesForDoctor: BookedConsultationTimeConnection;
  getBusiness: Business;
  getBusinessEmployees: UnPopulatedEmployeePayload;
  getCards: CardsConnections;
  getChatConversations: ChatConversationsConnection;
  getChatMessages: ChatMessagesPayload;
  getCompanies: CompanyConnection;
  getCompany: Company;
  getConsultation: Consultation;
  getConsultations: ConsultationConnection;
  getDiagnosis: DiagnosisConnection;
  getDiagnosticDashboard: DiagnosticDashboard;
  getDiagnosticLabTest: DiagnosticLabTest;
  getDiagnosticLabTests: DiagnosticLabTestsConnection;
  getDiagnosticTest: DiagnosticTest;
  getDiagnosticTests: DiagnosticTestsConnection;
  getDoctorAvailability: AvailabilityConnection;
  getDoctorAvailabilityForDate: Availability;
  getDoctorConsultationDetails: Scalars['JSON']['output'];
  getDoctorPatient: DoctorPatient;
  getDoctorPatients: DoctorPatientConnection;
  getDrugOrder: DrugOrder;
  getDrugOrders: DrugOrdersConnection;
  getDueConsultationNotificationSchedules: Scalars['JSON']['output'];
  getEarningStats: EarningsStat;
  getEmailList: AccountConnection;
  getEnrollee: Enrollee;
  getEnrollees: EnrolleesConnection;
  getExternalPlan: ExternalPlan;
  getExternalPlans: PlansPayload;
  getFavouriteDoctors: FavouriteDoctorsConnection;
  getFeaturedPartner: Partner;
  getHospitalMedications: HospitalMedicationsConnection;
  getIllness: IllnessOutput;
  getIllnesses: IllnessConnection;
  getIssue: IssueConnection;
  getIssues: IssuesConnection;
  getLGAsFromWellaHealth: WellaLgAsConnection;
  getLabResult: LabResultPayload;
  getLabResults: LabArrayPayload;
  getMedication: MedicationsPayload;
  getMedications: MedicationsArrayPayload;
  getMessage: Message;
  getMessages: MessageConnection;
  getMicroInsurancePlan: Scalars['JSON']['output'];
  getMicroInsuranceRevenue: Scalars['JSON']['output'];
  getMicroInsuranceStats: Scalars['JSON']['output'];
  getMyAppointments: AppointmentConnection;
  getMyConsultations: ConsultationConnection;
  getMyEarnings: EarningConnection;
  getMyPatients: DoctorPatientConnection;
  getMyTransactions: TransactionReferenceConnection;
  getNemEnrollee: Scalars['JSON']['output'];
  getNemPlanBenefit: Scalars['JSON']['output'];
  getNemPlanProviders: Scalars['JSON']['output'];
  getNemPlansForHeala: Scalars['JSON']['output'];
  getNewConsultation: Consultation;
  getNextConsultations: ConsultationConnection;
  getNotifications: NotificationsConnection;
  getPartner: Partner;
  getPartnerBySubdomain: PartnerConfiguration;
  getPartnerCategories: PartnerCategoriesConnection;
  getPartnerConfiguration: PartnerConfiguration;
  getPartners: PartnersConnection;
  getPatientConsultationDetails: Scalars['JSON']['output'];
  getPayoutDoctors: DoctorProfileArrayPayload;
  getPayouts: PayoutConnection;
  getPayoutsProviders: ProviderTypeConnection;
  getPermission: Permission;
  getPermissions: PermissionConnection;
  getPharmaciesFromWellaHealth: WellaHealthPharmaciesConnection;
  getPharmacyDashboard: PharmacyDashboard;
  getPharmacyDrugs: PharmacyDrugsConnection;
  getPlan: PlanPayload;
  getPlans: PlansPayload;
  getPrescription: Prescription;
  getPrescriptions: PrescriptionsConnection;
  getProvider: Provider;
  getProviders: AllProviderConnection;
  getReferral: Referral;
  getReferrals: ReferralConnection;
  getRejectedDrugs: RejectedPayload;
  getReminder: ReminderPayload;
  getReminders: ReminderArrayPayload;
  getRequests: RequestsConnection;
  getRole: Role;
  getRoles: Roles;
  getServerTime: ServerTime;
  getSignUpStats: Scalars['JSON']['output'];
  getSingleFamily: FamilyPayload;
  getSpecialization: Specialization;
  getSpecializations: SpecializationPayload;
  getStats: AllStats;
  getSubscription: Scalars['JSON']['output'];
  getSymptoms: SymptomsConnection;
  getTransactions: TransactionReferenceConnection;
  getUserFcmToken: FcmToken;
  getUserType: UserType;
  getUserTypeProviders: ProviderTypeConnection;
  getUserTypes: UserTypeConnection;
  getVerification: Verification;
  getVerifications: VerificationsConnection;
  hello: Scalars['String']['output'];
  me: SinglePopulatedAccount;
  nextStepsBusiness: Scalars['JSON']['output'];
  nextStepsFamily: Scalars['JSON']['output'];
  prescriptionReferral: PopulatedPrescriptionReferral;
  prescriptionReferrals: PrescriptionReferralPayload;
  profile: PopulatedProfile;
  profiles: ProfileConnection;
  profilesByPlan: ProfileConnection;
  profilesByStatus: ProfileConnection;
  quotes: QuotesPayload;
  sendNewsLetter: Scalars['Boolean']['output'];
  testApproval: PopulatedTestsApprovals;
  testApprovals: TestsApprovalsPayload;
};


export type QueryPatientCountArgs = {
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAccessConsultationArgs = {
  data: ConsultationId;
};


export type QueryAccessDoctorConsultationArgs = {
  data: ConsultationId;
};


export type QueryAccountArgs = {
  id: Scalars['String']['input'];
};


export type QueryAccountCountArgs = {
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAccountsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryActivityLogsArgs = {
  limit?: Scalars['Int']['input'];
  skip?: Scalars['Int']['input'];
};


export type QueryAllergiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAllergyArgs = {
  data: AllergyInput;
};


export type QueryBusinessArgs = {
  id: Scalars['Int']['input'];
};


export type QueryBusinessesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryCancelSubscriptionArgs = {
  data: UserPlanInput;
};


export type QueryCheckChargeArgs = {
  data: CheckChargeInput;
};


export type QueryCheckHasSubscriptionArgs = {
  data: UserPlanInput;
};


export type QueryCheckNemEnrolleeArgs = {
  data: GetSubscriptionInput;
};


export type QueryCountConsultationArgs = {
  data: ConsultationId;
};


export type QueryDeleteDiagnosticLabTestArgs = {
  data: DiagnosticInput;
};


export type QueryDeletePartnerArgs = {
  data: PartnerInput;
};


export type QueryDoctorCountArgs = {
  data?: InputMaybe<DoctorProfileCountInput>;
};


export type QueryDoctorProfileArgs = {
  data: DoctorProfileInput;
};


export type QueryDoctorProfileByEmailArgs = {
  data: DoctorProfileInput;
};


export type QueryDoctorProfilesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDoctorProfilesByStatusArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDrugApprovalArgs = {
  data: IdInput;
};


export type QueryDrugsApprovalsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryEmployeesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryExportConsultationsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};


export type QueryExportConsultationsNewArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};


export type QueryFamiliesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryFindMultipleDoctorProfilesArgs = {
  data: MultipleDoctorProfileInput;
};


export type QueryFindMultipleProfilesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  data: FindProfilesIdsInput;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGenerateConsultationsStatArgs = {
  data: GenerateConsultationStatsInput;
};


export type QueryGetAllAppointmentsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAllStatsArgs = {
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
};


export type QueryGetAppointmentArgs = {
  data: AppointmentInput;
};


export type QueryGetAppointmentsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAvailabilitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAvailabilityArgs = {
  data: AvailableIdInput;
};


export type QueryGetAvailableDoctorsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  date: Scalars['String']['input'];
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetAvailableTimeArgs = {
  data: AvailableTimeIdInput;
};


export type QueryGetAvailableTimesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetBookedConsultationTimesForDoctorArgs = {
  data: BookedConsultationTimeInput;
};


export type QueryGetBusinessArgs = {
  data: GetBusinessInput;
};


export type QueryGetBusinessEmployeesArgs = {
  data: GetBusinessEmployeesInput;
};


export type QueryGetCardsArgs = {
  data: CardInput;
};


export type QueryGetChatMessagesArgs = {
  data: MessagingConsultationInput;
};


export type QueryGetCompaniesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetCompanyArgs = {
  data: CompanyInput;
};


export type QueryGetConsultationArgs = {
  data: ConsultationId;
};


export type QueryGetConsultationsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetDiagnosisArgs = {
  gender: Scalars['String']['input'];
  symptoms: Array<Scalars['Int']['input']>;
  year_of_birth: Scalars['Float']['input'];
};


export type QueryGetDiagnosticDashboardArgs = {
  data: GetDiagnosticDashboardInput;
};


export type QueryGetDiagnosticLabTestArgs = {
  data: DiagnosticInput;
};


export type QueryGetDiagnosticLabTestsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetDiagnosticTestArgs = {
  data: DiagnosticInput;
};


export type QueryGetDiagnosticTestsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetDoctorAvailabilityArgs = {
  data: AvailableIdInput;
};


export type QueryGetDoctorAvailabilityForDateArgs = {
  data: AvailableDoctorTimesInput;
};


export type QueryGetDoctorConsultationDetailsArgs = {
  data: ConsultationId;
};


export type QueryGetDoctorPatientArgs = {
  data: DoctorPatientIdInput;
};


export type QueryGetDoctorPatientsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetDrugOrderArgs = {
  data: DrugOrderInput;
};


export type QueryGetDrugOrdersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetDueConsultationNotificationSchedulesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetEarningStatsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetEmailListArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetEnrolleeArgs = {
  data: DeleteEnrolleeInput;
};


export type QueryGetEnrolleesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetExternalPlanArgs = {
  data: ExternalPlanInput;
};


export type QueryGetExternalPlansArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetFavouriteDoctorsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetFeaturedPartnerArgs = {
  data: GetFeaturedPartnerInput;
};


export type QueryGetHospitalMedicationsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetIllnessArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetIllnessesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetIssueArgs = {
  issueId: Scalars['Float']['input'];
};


export type QueryGetLgAsFromWellaHealthArgs = {
  data: WellaHealthPharmacyInput;
};


export type QueryGetLabResultArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetLabResultsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetMedicationArgs = {
  data: PatientMedicationInput;
};


export type QueryGetMedicationsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetMessageArgs = {
  data: MessagingInput;
};


export type QueryGetMessagesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetMicroInsurancePlanArgs = {
  filterBy: FilterPlansInput;
};


export type QueryGetMicroInsuranceRevenueArgs = {
  filterBy?: InputMaybe<FilterRegistrationsInput>;
};


export type QueryGetMicroInsuranceStatsArgs = {
  filterBy: StatisticsInput;
};


export type QueryGetMyAppointmentsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetMyConsultationsArgs = {
  q: Scalars['String']['input'];
};


export type QueryGetMyEarningsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetMyTransactionsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetNemEnrolleeArgs = {
  data: HmoIdInput;
};


export type QueryGetNemPlanBenefitArgs = {
  data: PlanCodeInput;
};


export type QueryGetNemPlanProvidersArgs = {
  data: PlanCodeInput;
};


export type QueryGetNewConsultationArgs = {
  data: ConsultationId;
};


export type QueryGetNextConsultationsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetNotificationsArgs = {
  filter?: InputMaybe<FilterInputType>;
  user: Scalars['String']['input'];
};


export type QueryGetPartnerArgs = {
  data: PartnerInput;
};


export type QueryGetPartnerBySubdomainArgs = {
  data: GetPartnerSubdomainInput;
};


export type QueryGetPartnerCategoriesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPartnerConfigurationArgs = {
  data: GetPartnerConfigurationInput;
};


export type QueryGetPartnersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPatientConsultationDetailsArgs = {
  data: ConsultationId;
};


export type QueryGetPayoutDoctorsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPayoutsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPayoutsProvidersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPermissionArgs = {
  data: PermissionInput;
};


export type QueryGetPermissionsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPharmaciesFromWellaHealthArgs = {
  data: WellaHealthPharmacyInput;
};


export type QueryGetPharmacyDashboardArgs = {
  data: GetPharmacyDashboardInput;
};


export type QueryGetPharmacyDrugsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetPlanArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetPlansArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPrescriptionArgs = {
  data: AddPrescription;
};


export type QueryGetPrescriptionsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetProviderArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetProvidersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetReferralArgs = {
  data: GetReferralInput;
};


export type QueryGetReferralsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetRejectedDrugsArgs = {
  data: IdInput;
};


export type QueryGetReminderArgs = {
  data: ReminderInput;
};


export type QueryGetRemindersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetRequestsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetRoleArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGetRolesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSignUpStatsArgs = {
  filterBy?: InputMaybe<FilterRegistrationsInput>;
};


export type QueryGetSingleFamilyArgs = {
  data: FamilyInput;
};


export type QueryGetSpecializationArgs = {
  data: IdInput;
};


export type QueryGetSpecializationsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetStatsArgs = {
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
};


export type QueryGetSubscriptionArgs = {
  data?: InputMaybe<GetSubscriptionInput>;
};


export type QueryGetTransactionsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserFcmTokenArgs = {
  user: Scalars['String']['input'];
};


export type QueryGetUserTypeArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetUserTypeProvidersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserTypesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetVerificationArgs = {
  data: GetVerificationInput;
};


export type QueryGetVerificationsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPrescriptionReferralArgs = {
  data: PrescriptionReferralInput;
};


export type QueryPrescriptionReferralsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryProfileArgs = {
  id: Scalars['String']['input'];
};


export type QueryProfilesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryProfilesByPlanArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryProfilesByStatusArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryQuotesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySendNewsLetterArgs = {
  data: NewsLetterInput;
};


export type QueryTestApprovalArgs = {
  data: IdInput;
};


export type QueryTestApprovalsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<Scalars['JSON']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  q?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type Quote = {
  __typename?: 'Quote';
  comment?: Maybe<Scalars['String']['output']>;
  companyName?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  noOfEmployees?: Maybe<Scalars['Float']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type QuoteInput = {
  quoteId: Scalars['String']['input'];
};

export type QuoteStatusInput = {
  quoteId: Scalars['String']['input'];
  status: Scalars['String']['input'];
};

export type QuotesPayload = {
  __typename?: 'QuotesPayload';
  data?: Maybe<Array<Quote>>;
  pageInfo?: Maybe<PageInfo>;
};

export type Rating = {
  __typename?: 'Rating';
  comment?: Maybe<Scalars['String']['output']>;
  review?: Maybe<Scalars['String']['output']>;
  score?: Maybe<Scalars['Int']['output']>;
};

export type RebroadCastNotificationInput = {
  id: Scalars['ID']['input'];
};

export type Reference = {
  reference_code: Scalars['String']['input'];
};

export type Referral = {
  __typename?: 'Referral';
  _id: Scalars['ID']['output'];
  consultationId?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<DoctorProfile>;
  facility?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  patient?: Maybe<Profile>;
  providerId?: Maybe<Scalars['String']['output']>;
  provisionalDiagnosis?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  specialization?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  testType?: Maybe<Scalars['String']['output']>;
  tests?: Maybe<Array<DiagnosticLabTest>>;
  trackingId?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  urgency?: Maybe<Scalars['String']['output']>;
};

export type ReferralConnection = {
  __typename?: 'ReferralConnection';
  pageInfo?: Maybe<PageInfo>;
  referral: Array<Referral>;
};

export type ReferralDosageFrequency = {
  __typename?: 'ReferralDosageFrequency';
  duration?: Maybe<Scalars['Float']['output']>;
  timing?: Maybe<Scalars['Float']['output']>;
};

export type ReferralPayload = {
  __typename?: 'ReferralPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  referral?: Maybe<Referral>;
};

export type RefreshPayload = {
  __typename?: 'RefreshPayload';
  access_token: Scalars['String']['output'];
  message?: Maybe<Scalars['String']['output']>;
  refresh_token: Scalars['String']['output'];
};

export type RegisterBusinessInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  name: Scalars['String']['input'];
  noOfEmployees: Scalars['Float']['input'];
  password: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type RegisterBusinessPayload = {
  __typename?: 'RegisterBusinessPayload';
  access_token?: Maybe<Scalars['String']['output']>;
  account?: Maybe<Account>;
  business?: Maybe<Business>;
  profile?: Maybe<Profile>;
  refresh_token?: Maybe<Scalars['String']['output']>;
};

export type RegisterFamilyInput = {
  dob: Scalars['String']['input'];
  email: Scalars['String']['input'];
  externalProvider: Scalars['String']['input'];
  externalProviderId: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  gender: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  noOfDependant: Scalars['Float']['input'];
  password: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
};

export type RegisterFamilyPayload = {
  __typename?: 'RegisterFamilyPayload';
  access_token?: Maybe<Scalars['String']['output']>;
  account?: Maybe<Account>;
  profile?: Maybe<Profile>;
  refresh_token?: Maybe<Scalars['String']['output']>;
};

export type RegisterMicroInsuranceIndividualInput = {
  dob: Scalars['String']['input'];
  email: Scalars['String']['input'];
  externalProvider: Scalars['String']['input'];
  externalProviderId: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  gender: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  password: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
};

export type RegisterMicroInsuranceIndividualPayload = {
  __typename?: 'RegisterMicroInsuranceIndividualPayload';
  access_token?: Maybe<Scalars['String']['output']>;
  account?: Maybe<Account>;
  profile?: Maybe<Profile>;
  refresh_token?: Maybe<Scalars['String']['output']>;
};

export type RejectVerificationInput = {
  reason: Scalars['String']['input'];
  verificationId: Scalars['String']['input'];
};

export type RejectedPayload = {
  __typename?: 'RejectedPayload';
  account?: Maybe<Account>;
  drugs?: Maybe<Array<Drug>>;
};

export type RejectionVerificationPayload = {
  __typename?: 'RejectionVerificationPayload';
  message: Scalars['String']['output'];
  reason: Scalars['String']['output'];
};

export type Reminder = {
  __typename?: 'Reminder';
  _id?: Maybe<Scalars['ID']['output']>;
  createdAt: Scalars['DateTime']['output'];
  date: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  interval: Scalars['String']['output'];
  patient: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type ReminderArrayPayload = {
  __typename?: 'ReminderArrayPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  message: Scalars['String']['output'];
  pageInfo: PageInfo;
  reminder: Array<Reminder>;
};

export type ReminderInput = {
  id: Scalars['String']['input'];
};

export type ReminderPayload = {
  __typename?: 'ReminderPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  message: Scalars['String']['output'];
  reminder: Reminder;
};

export type RemoveDependantInput = {
  dependantId: Scalars['String']['input'];
  employeeId: Scalars['String']['input'];
};

export type RenewLicenseInput = {
  expiry_date: Scalars['String']['input'];
  image: Scalars['String']['input'];
  number: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type Request = {
  __typename?: 'Request';
  _id: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  firstName: Scalars['String']['output'];
  lastName: Scalars['String']['output'];
  location: Scalars['String']['output'];
  name: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type RequestPayload = {
  __typename?: 'RequestPayload';
  errors?: Maybe<Array<ErrorPayload>>;
  message: Scalars['String']['output'];
  request: Request;
};

export type RequestReferralInput = {
  consultationId?: InputMaybe<Scalars['String']['input']>;
  doctor: Scalars['String']['input'];
  facility?: InputMaybe<Scalars['String']['input']>;
  note: Scalars['String']['input'];
  patient: Scalars['String']['input'];
  provisionalDiagnosis?: InputMaybe<Scalars['String']['input']>;
  reason: Scalars['String']['input'];
  specialization?: InputMaybe<Scalars['String']['input']>;
  tests?: InputMaybe<Array<DiagnosticLabTestInput>>;
  type: Scalars['String']['input'];
  urgency?: InputMaybe<Scalars['String']['input']>;
};

export type RequestsConnection = {
  __typename?: 'RequestsConnection';
  data: Array<Request>;
  pageInfo: PageInfo;
};

export type RescheduleConsultationInput = {
  doctor: Scalars['ID']['input'];
  id: Scalars['ID']['input'];
  rescheduleReason: Scalars['String']['input'];
  time: Scalars['String']['input'];
};

export type ResendEmployeeInput = {
  businessId: Scalars['String']['input'];
  email: Scalars['String']['input'];
};

export type ResendInput = {
  email: Scalars['String']['input'];
};

export type ResetInput = {
  email: Scalars['String']['input'];
};

export type ResolveDisputeInput = {
  disputeResolvedReason: Scalars['String']['input'];
  disputeStatus: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  isDisputed: Scalars['Boolean']['input'];
  status?: InputMaybe<Scalars['String']['input']>;
};

export type RestoreMyAccountInput = {
  email: Scalars['String']['input'];
  password?: InputMaybe<Scalars['String']['input']>;
};

export type Role = {
  __typename?: 'Role';
  _id?: Maybe<Scalars['ID']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  editable?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  permissions?: Maybe<Array<Scalars['String']['output']>>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type Roles = {
  __typename?: 'Roles';
  pageInfo?: Maybe<PageInfo>;
  role: Array<Role>;
};

export type SendNotificationsInput = {
  content: Scalars['String']['input'];
  dryRun?: InputMaybe<Scalars['Boolean']['input']>;
  fcmToken?: InputMaybe<Scalars['String']['input']>;
  fcmTokens?: InputMaybe<Array<Scalars['String']['input']>>;
  group?: InputMaybe<Scalars['String']['input']>;
  itemId: Scalars['ID']['input'];
  previewImageUri?: InputMaybe<Scalars['String']['input']>;
  previewImageUriThumbnail?: InputMaybe<Scalars['String']['input']>;
  role?: InputMaybe<Scalars['String']['input']>;
  saveNotification?: InputMaybe<Scalars['Boolean']['input']>;
  tag: Scalars['String']['input'];
  ticker: Scalars['String']['input'];
  title: Scalars['String']['input'];
  useSound?: InputMaybe<Scalars['Boolean']['input']>;
  users?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type ServerTime = {
  __typename?: 'ServerTime';
  hourMin: Scalars['String']['output'];
  hourMinLagos: Scalars['String']['output'];
  time: Scalars['String']['output'];
  timeJSdate: Scalars['String']['output'];
  timeLagos: Scalars['String']['output'];
  timeLagosJsDate: Scalars['String']['output'];
};

export type SetAvailabilityInput = {
  available: Scalars['Boolean']['input'];
  day: Scalars['String']['input'];
  doctor: Scalars['String']['input'];
  providerId: Scalars['String']['input'];
  times: Array<AvailabilityArray>;
};

export type SetPermanentPassword = {
  email: Scalars['String']['input'];
  newPassword: Scalars['String']['input'];
  temporaryPassword: Scalars['String']['input'];
};

export type Setting = {
  __typename?: 'Setting';
  _id?: Maybe<Scalars['ID']['output']>;
  key?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type SettingPayload = {
  __typename?: 'SettingPayload';
  errors: Array<ErrorPayload>;
  message?: Maybe<Scalars['String']['output']>;
  setting?: Maybe<Setting>;
};

export type SignupPayload = {
  __typename?: 'SignupPayload';
  account: Account;
  message: Scalars['String']['output'];
};

export type SignupUserInput = {
  authType: Scalars['String']['input'];
  email: Scalars['String']['input'];
  enrolleeNumber?: InputMaybe<Scalars['String']['input']>;
  password: Scalars['String']['input'];
  providerId?: InputMaybe<Scalars['String']['input']>;
  referralCode?: InputMaybe<Scalars['String']['input']>;
  role: Scalars['String']['input'];
  type?: InputMaybe<Scalars['String']['input']>;
  userTypeId?: InputMaybe<Scalars['String']['input']>;
};

export type SinglePopulatedAccount = {
  __typename?: 'SinglePopulatedAccount';
  _id?: Maybe<Scalars['ID']['output']>;
  access_token?: Maybe<Scalars['String']['output']>;
  apiKey?: Maybe<Scalars['String']['output']>;
  authType?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deactivateType?: Maybe<Scalars['String']['output']>;
  deactivated?: Maybe<Scalars['Boolean']['output']>;
  deactivatedAt?: Maybe<Scalars['String']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enrolleeNumber?: Maybe<Scalars['String']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  isEmailVerified?: Maybe<Scalars['Boolean']['output']>;
  isPasswordTemporary?: Maybe<Scalars['Boolean']['output']>;
  nextStep?: Maybe<Scalars['String']['output']>;
  otpTokenExpiresAt?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<ProviderType>;
  refresh_token?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userTypeId?: Maybe<UserAccountType>;
};

export type SinglePrescription = {
  __typename?: 'SinglePrescription';
  _id: Scalars['ID']['output'];
  consultation?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<Scalars['String']['output']>;
  drugs?: Maybe<Array<PrescriptionDrug>>;
  patient?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type SingleReferral = {
  __typename?: 'SingleReferral';
  _id: Scalars['ID']['output'];
  consultationId?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  patient?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  provisionalDiagnosis?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  specialization?: Maybe<Scalars['String']['output']>;
  tests?: Maybe<Array<DiagnosticLabTest>>;
  trackingId?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  urgency?: Maybe<Scalars['String']['output']>;
};

export type SocialSignupUserInput = {
  authType: Scalars['String']['input'];
  deviceId: Scalars['String']['input'];
  email: Scalars['String']['input'];
  enrolleeNumber?: InputMaybe<Scalars['String']['input']>;
  isMicroInsurance?: InputMaybe<Scalars['Boolean']['input']>;
  isMicroInsuranceBusiness?: InputMaybe<Scalars['Boolean']['input']>;
  isMicroInsuranceBusinessPrincipal?: InputMaybe<Scalars['Boolean']['input']>;
  isMicroInsuranceFamily?: InputMaybe<Scalars['Boolean']['input']>;
  isMicroInsuranceFamilyPrincipal?: InputMaybe<Scalars['Boolean']['input']>;
  isMicroInsuranceUser?: InputMaybe<Scalars['Boolean']['input']>;
  providerId?: InputMaybe<Scalars['String']['input']>;
  referralCode?: InputMaybe<Scalars['String']['input']>;
  role: Scalars['String']['input'];
  userTypeId?: InputMaybe<Scalars['String']['input']>;
};

export type SpecialisationArray = {
  __typename?: 'SpecialisationArray';
  Accuracy?: Maybe<Scalars['String']['output']>;
  ID?: Maybe<Scalars['String']['output']>;
  Name?: Maybe<Scalars['String']['output']>;
  Ranking?: Maybe<Scalars['String']['output']>;
  SpecialistID?: Maybe<Scalars['String']['output']>;
};

export type Specialist = {
  __typename?: 'Specialist';
  _id?: Maybe<Scalars['String']['output']>;
  approved?: Maybe<Scalars['String']['output']>;
  facility?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  specialization?: Maybe<Scalars['String']['output']>;
  trackingId?: Maybe<Scalars['String']['output']>;
  urgency?: Maybe<Scalars['String']['output']>;
};

export type SpecialistInput = {
  facility?: InputMaybe<Scalars['String']['input']>;
  note?: InputMaybe<Scalars['String']['input']>;
  reason?: InputMaybe<Scalars['String']['input']>;
  specialization?: InputMaybe<Scalars['String']['input']>;
  trackingId?: InputMaybe<Scalars['String']['input']>;
  urgency?: InputMaybe<Scalars['String']['input']>;
};

export type Specialization = {
  __typename?: 'Specialization';
  _id?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type SpecializationPayload = {
  __typename?: 'SpecializationPayload';
  data?: Maybe<Array<Specialization>>;
  pageInfo?: Maybe<PageInfo>;
};

export type Stat = {
  __typename?: 'Stat';
  acceptedChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  activeChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  cancelledChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  chartData?: Maybe<Array<Scalars['JSON']['output']>>;
  completedChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  declinedChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  diagnosticsChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  hospitalChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  inactiveChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  ongoingChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  pendingChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  pharmacyChartData?: Maybe<Array<Scalars['JSON']['output']>>;
  total?: Maybe<Scalars['Float']['output']>;
  totalAccepted?: Maybe<Scalars['Float']['output']>;
  totalActive?: Maybe<Scalars['Float']['output']>;
  totalCancelled?: Maybe<Scalars['Float']['output']>;
  totalCompleted?: Maybe<Scalars['Float']['output']>;
  totalDeclined?: Maybe<Scalars['Float']['output']>;
  totalDiagnostics?: Maybe<Scalars['Float']['output']>;
  totalHospitals?: Maybe<Scalars['Float']['output']>;
  totalInactive?: Maybe<Scalars['Float']['output']>;
  totalOngoing?: Maybe<Scalars['Float']['output']>;
  totalPending?: Maybe<Scalars['Float']['output']>;
  totalPharmacies?: Maybe<Scalars['Float']['output']>;
};

export type StatisticsInput = {
  month: Scalars['Float']['input'];
  year?: InputMaybe<Scalars['Float']['input']>;
};

export type Stats = {
  __typename?: 'Stats';
  appointmentStats?: Maybe<AppointmentStats>;
  availabilityCalendar?: Maybe<AvailableTimeStats>;
  doctorStats?: Maybe<DaysStats>;
  patientStats?: Maybe<DaysStats>;
  subscribers?: Maybe<DaysStats>;
  totalEarnings?: Maybe<Scalars['Float']['output']>;
  totalPayout?: Maybe<Scalars['Float']['output']>;
};

export type SubscriptionType = {
  __typename?: 'SubscriptionType';
  _id?: Maybe<Scalars['String']['output']>;
  amount?: Maybe<Scalars['Float']['output']>;
  emailToken?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<PlanType>;
  providerId?: Maybe<Scalars['ID']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscriptionCode?: Maybe<Scalars['String']['output']>;
  subscriptionExpiresAt?: Maybe<Scalars['DateTime']['output']>;
  subscriptionPurchasedAt?: Maybe<Scalars['DateTime']['output']>;
  usages?: Maybe<Scalars['Float']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type Symp = {
  __typename?: 'Symp';
  name?: Maybe<Scalars['String']['output']>;
};

export type Symptom = {
  name: Scalars['String']['input'];
};

export type Symptoms = {
  __typename?: 'Symptoms';
  Description?: Maybe<Scalars['String']['output']>;
  DescriptionShort?: Maybe<Scalars['String']['output']>;
  ID?: Maybe<Scalars['String']['output']>;
  MedicalCondition?: Maybe<Scalars['String']['output']>;
  Name?: Maybe<Scalars['String']['output']>;
  PossibleSymptoms?: Maybe<Scalars['String']['output']>;
  ProfName?: Maybe<Scalars['String']['output']>;
  Synonyms?: Maybe<Scalars['String']['output']>;
  TreatmentDescription?: Maybe<Scalars['String']['output']>;
  _id?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTimeScalar']['output']>;
  updatedAt?: Maybe<Scalars['DateTimeScalar']['output']>;
};

export type SymptomsConnection = {
  __typename?: 'SymptomsConnection';
  symptoms: Array<Symptoms>;
};

export type SymptomsDiagnosis = {
  __typename?: 'SymptomsDiagnosis';
  Issue: IssueObj;
  Specialisation: Array<SpecialisationArray>;
};

export type Test = {
  __typename?: 'Test';
  _id?: Maybe<Scalars['String']['output']>;
  approved?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
  tat?: Maybe<Scalars['String']['output']>;
  urgency?: Maybe<Scalars['String']['output']>;
};

export type TestInput = {
  name: Scalars['String']['input'];
  notes?: InputMaybe<Scalars['String']['input']>;
  price: Scalars['Float']['input'];
  tat?: InputMaybe<Scalars['String']['input']>;
  urgency: Scalars['String']['input'];
};

export type TestResults = {
  __typename?: 'TestResults';
  file?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type TestResultsInput = {
  file: Scalars['String']['input'];
  title: Scalars['String']['input'];
};

export type TestsApprovals = {
  __typename?: 'TestsApprovals';
  _id?: Maybe<Scalars['String']['output']>;
  consultation?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  doctor?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Scalars['String']['output']>;
  patient?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  referral?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tests?: Maybe<Array<Test>>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type TestsApprovalsPayload = {
  __typename?: 'TestsApprovalsPayload';
  data?: Maybe<Array<PopulatedTestsApprovals>>;
  pageInfo?: Maybe<PageInfo>;
};

export type Ticket = {
  __typename?: 'Ticket';
  _id: Scalars['ID']['output'];
  createdAt?: Maybe<Scalars['String']['output']>;
  createdBy?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  priority?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['String']['output']>;
};

export type TicketPayload = {
  __typename?: 'TicketPayload';
  message?: Maybe<Scalars['String']['output']>;
  ticket?: Maybe<Ticket>;
};

export type TransactionReference = {
  __typename?: 'TransactionReference';
  _id: Scalars['String']['output'];
  amount: Scalars['String']['output'];
  brand: Scalars['String']['output'];
  createdAt: Scalars['DateTimeScalar']['output'];
  failedPermanently: Scalars['String']['output'];
  itemId?: Maybe<Scalars['String']['output']>;
  last4: Scalars['String']['output'];
  paymentGateway: Scalars['String']['output'];
  reason: Scalars['String']['output'];
  reference: Scalars['String']['output'];
  retries: Scalars['String']['output'];
  saveCard: Scalars['Boolean']['output'];
  status?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTimeScalar']['output'];
  used: Scalars['Boolean']['output'];
  user: Scalars['String']['output'];
};

export type TransactionReferenceConnection = {
  __typename?: 'TransactionReferenceConnection';
  data: Array<TransactionReference>;
  pageInfo: PageInfo;
};

export type UnPopulatedConsultation = {
  __typename?: 'UnPopulatedConsultation';
  _id: Scalars['ID']['output'];
  appointmentAcceptedAt?: Maybe<Scalars['String']['output']>;
  appointmentStartedAt?: Maybe<Scalars['String']['output']>;
  companyId?: Maybe<Scalars['String']['output']>;
  consultationDuration?: Maybe<Scalars['String']['output']>;
  consultationOwner?: Maybe<Scalars['String']['output']>;
  contactMedium?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  createdThrough?: Maybe<Scalars['String']['output']>;
  declineReason?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  diagnosis?: Maybe<Array<Diag>>;
  discomfortLevel?: Maybe<Scalars['String']['output']>;
  disputeReason?: Maybe<Scalars['String']['output']>;
  disputeResolvedReason?: Maybe<Scalars['String']['output']>;
  disputeStatus?: Maybe<Scalars['String']['output']>;
  doctor?: Maybe<Scalars['String']['output']>;
  doctorEndCommunicationReason?: Maybe<Scalars['String']['output']>;
  doctorJoined?: Maybe<Scalars['Boolean']['output']>;
  doctorNote?: Maybe<Scalars['String']['output']>;
  doctorSatisfactionReason?: Maybe<Scalars['String']['output']>;
  doctorSatisfied?: Maybe<Scalars['Boolean']['output']>;
  externalPharmacy?: Maybe<Scalars['String']['output']>;
  externalProvider?: Maybe<Scalars['String']['output']>;
  fee?: Maybe<Scalars['Int']['output']>;
  firstNotice?: Maybe<Scalars['String']['output']>;
  followUpConsultationId?: Maybe<Scalars['JSON']['output']>;
  isDisputeResolved?: Maybe<Scalars['Boolean']['output']>;
  isDisputed?: Maybe<Scalars['Boolean']['output']>;
  isFollowUp?: Maybe<Scalars['Boolean']['output']>;
  joined?: Maybe<Scalars['Boolean']['output']>;
  notificationSchedules?: Maybe<Array<NotificationSchedule>>;
  paid?: Maybe<Scalars['Boolean']['output']>;
  path?: Maybe<Scalars['String']['output']>;
  patient?: Maybe<Scalars['String']['output']>;
  patientEndCommunicationReason?: Maybe<Scalars['String']['output']>;
  patientJoined?: Maybe<Scalars['Boolean']['output']>;
  patientSatisfactionReason?: Maybe<Scalars['String']['output']>;
  patientSatisfied?: Maybe<Scalars['Boolean']['output']>;
  pharmacyAddress?: Maybe<Scalars['String']['output']>;
  pharmacyCode?: Maybe<Scalars['String']['output']>;
  pharmacyName?: Maybe<Scalars['String']['output']>;
  prescription?: Maybe<Array<Prescription>>;
  principalHmoId?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Rating>;
  reason?: Maybe<Scalars['String']['output']>;
  referralId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  symptoms?: Maybe<Array<Symp>>;
  time?: Maybe<Scalars['DateTime']['output']>;
  trackingId?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  wasDisputed?: Maybe<Scalars['Boolean']['output']>;
};

export type UnPopulatedDoctorProfile = {
  __typename?: 'UnPopulatedDoctorProfile';
  _id?: Maybe<Scalars['ID']['output']>;
  accountDetails?: Maybe<Scalars['String']['output']>;
  accountId?: Maybe<Scalars['String']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  balance?: Maybe<Scalars['Int']['output']>;
  cadre?: Maybe<Scalars['String']['output']>;
  consultationReminderNotification?: Maybe<Scalars['Boolean']['output']>;
  consultationRequestNotification?: Maybe<Scalars['Boolean']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  dob?: Maybe<Scalars['DateTime']['output']>;
  dociId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  fee?: Maybe<Scalars['Float']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  hospital?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  instantConsultationNotification?: Maybe<Scalars['Boolean']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  picture?: Maybe<Scalars['String']['output']>;
  providerId?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Int']['output']>;
  specialization?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type UnPopulatedEmployee = {
  __typename?: 'UnPopulatedEmployee';
  businessId?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  dob?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  noofdependants?: Maybe<Scalars['Float']['output']>;
  relationship?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type UnPopulatedEmployeePayload = {
  __typename?: 'UnPopulatedEmployeePayload';
  employees?: Maybe<Array<UnPopulatedEmployee>>;
};

export type UpdateAllergyInput = {
  food?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  medication?: InputMaybe<Scalars['String']['input']>;
  severity?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAppointmentInput = {
  date?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  doctor?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  patient?: InputMaybe<Scalars['String']['input']>;
  providerId?: InputMaybe<Scalars['String']['input']>;
  time?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAvailableTimeInput = {
  day?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  providerId?: InputMaybe<Scalars['String']['input']>;
  times?: InputMaybe<Array<AvailabilityArray>>;
};

export type UpdateBusinessInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  noOfEmployees?: InputMaybe<Scalars['Float']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateCompanyInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  logo?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  providerId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateConsultationInput = {
  consultationId?: InputMaybe<Scalars['String']['input']>;
  consultationOwner?: InputMaybe<Scalars['String']['input']>;
  contactMedium?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  diagnosis?: InputMaybe<Array<Diagnosis>>;
  discomfortLevel?: InputMaybe<Scalars['String']['input']>;
  doctor?: InputMaybe<Scalars['String']['input']>;
  doctorJoined?: InputMaybe<Scalars['Boolean']['input']>;
  doctorNote?: InputMaybe<Scalars['String']['input']>;
  firstNotice?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  joined?: InputMaybe<Scalars['Boolean']['input']>;
  messages?: InputMaybe<Array<ChatMessageInput>>;
  patient?: InputMaybe<Scalars['String']['input']>;
  patientJoined?: InputMaybe<Scalars['Boolean']['input']>;
  pharmacyAddress?: InputMaybe<Scalars['String']['input']>;
  pharmacyName?: InputMaybe<Scalars['String']['input']>;
  providerId?: InputMaybe<Scalars['String']['input']>;
  reason?: InputMaybe<Scalars['String']['input']>;
  referralId?: InputMaybe<Scalars['String']['input']>;
  scheduleIds?: InputMaybe<Array<Scalars['String']['input']>>;
  serverTime?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  symptoms?: InputMaybe<Array<Symptom>>;
  time?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateDiagnosticLabTestInput = {
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  partner?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  tat?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateDoctorPatient = {
  doctor: Scalars['String']['input'];
  id: Scalars['String']['input'];
  patient: Scalars['String']['input'];
};

export type UpdateDoctorProfileInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  cadre?: InputMaybe<Scalars['String']['input']>;
  consultationReminderNotification?: InputMaybe<Scalars['Boolean']['input']>;
  consultationRequestNotification?: InputMaybe<Scalars['Boolean']['input']>;
  dob?: InputMaybe<Scalars['DateTime']['input']>;
  fee?: InputMaybe<Scalars['Float']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  hospital?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  instantConsultationNotification?: InputMaybe<Scalars['Boolean']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
  picture?: InputMaybe<Scalars['String']['input']>;
  specialization?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateDoctorProviderInput = {
  dociId: Scalars['String']['input'];
  providerId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateDoctorProviderPayload = {
  __typename?: 'UpdateDoctorProviderPayload';
  account?: Maybe<Account>;
  profile?: Maybe<DoctorProfile>;
};

export type UpdateDrugInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  approved?: InputMaybe<Scalars['String']['input']>;
  dosageFrequency?: InputMaybe<DosageFrequencyInput>;
  dosageQuantity?: InputMaybe<Scalars['Float']['input']>;
  dosageUnit?: InputMaybe<Scalars['String']['input']>;
  drugForm?: InputMaybe<Scalars['String']['input']>;
  drugName?: InputMaybe<Scalars['String']['input']>;
  drugPrice?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['String']['input'];
  instructions?: InputMaybe<Scalars['String']['input']>;
  markedUpPrice?: InputMaybe<Scalars['Float']['input']>;
  markup?: InputMaybe<Scalars['Float']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  priceListId?: InputMaybe<Scalars['Float']['input']>;
  quantity?: InputMaybe<Scalars['Float']['input']>;
  referralId: Scalars['String']['input'];
  route?: InputMaybe<Scalars['String']['input']>;
  unitPrice?: InputMaybe<Scalars['Float']['input']>;
};

export type UpdateDrugOrderInput = {
  deliveryOption?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  note?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  userLocation?: InputMaybe<UserLocationInput>;
};

export type UpdateEmailInput = {
  email: Scalars['String']['input'];
  password?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateEmployeeInput = {
  dob?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  employeeId: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateEnrolleeInput = {
  client?: InputMaybe<Scalars['String']['input']>;
  companyId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  expiryDate?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  hmoId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  lastName?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  photo?: InputMaybe<Scalars['String']['input']>;
  plan?: InputMaybe<Scalars['String']['input']>;
  planId?: InputMaybe<Scalars['String']['input']>;
  providerId?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateFamilyInput = {
  admin?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  image?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
  profileId?: InputMaybe<Scalars['String']['input']>;
  relationship?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateFcmTokenInput = {
  deviceId: Scalars['String']['input'];
  role: Scalars['String']['input'];
  token: Scalars['String']['input'];
  user: Scalars['String']['input'];
};

export type UpdateHospitalMedicationInput = {
  deliveryOption?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  note?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  userLocation?: InputMaybe<UserLocationInput>;
};

export type UpdateIllnessInput = {
  description: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  name: Scalars['String']['input'];
};

export type UpdateInsurancePlanInput = {
  benefits?: InputMaybe<Array<BenefitInput>>;
  id: Scalars['String']['input'];
  image?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateJoinedInput = {
  doctorJoined?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['String']['input'];
  patientJoined?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateLabInput = {
  doctor?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  partner?: InputMaybe<Scalars['String']['input']>;
  patient?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePartnerInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  bankDetails?: InputMaybe<BankDetailsInput>;
  category?: InputMaybe<Scalars['String']['input']>;
  classification?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  logoImageUrl?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  providerId?: InputMaybe<Scalars['String']['input']>;
  specialisation?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePasswordInput = {
  newPassword: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type UpdatePatientIllnessInput = {
  pastIllness: Array<AddPastIllnessInput>;
};

export type UpdatePatientMedicationInput = {
  dosage?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['String']['input'];
  interval?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePayStackPlanInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  interval?: InputMaybe<Scalars['String']['input']>;
  invoice_limit?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  send_invoices?: InputMaybe<Scalars['Boolean']['input']>;
  send_sms?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdatePermissionInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePharmacyDrugInput = {
  drugName: Scalars['String']['input'];
  drugPrice: Scalars['Float']['input'];
  id: Scalars['String']['input'];
  notes: Scalars['String']['input'];
  partner: Scalars['String']['input'];
  priceListId: Scalars['Float']['input'];
  quantity: Scalars['Float']['input'];
  unitPrice: Scalars['Float']['input'];
};

export type UpdatePharmacyDrugPayload = {
  __typename?: 'UpdatePharmacyDrugPayload';
  errors: Array<ErrorPayload>;
  pharmacyDrug: PharmacyDrug;
};

export type UpdatePlanInput = {
  allowedFeatures?: InputMaybe<Scalars['JSON']['input']>;
  amount?: InputMaybe<Scalars['Float']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  duration?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  noOfDoctors?: InputMaybe<Scalars['Float']['input']>;
  provider?: InputMaybe<Scalars['ID']['input']>;
  specialisation?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePrescriptionReferralInput = {
  drugs?: InputMaybe<Array<Scalars['String']['input']>>;
  drugsPartner?: InputMaybe<Scalars['String']['input']>;
  referralId: Scalars['String']['input'];
  specialist?: InputMaybe<SpecialistInput>;
  status: Scalars['String']['input'];
  tests?: InputMaybe<Array<Scalars['String']['input']>>;
  testsPartner?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePriority = {
  end?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['String']['input'];
  start?: InputMaybe<Scalars['Float']['input']>;
};

export type UpdateProfileInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  bloodGroup?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['String']['input']>;
  externalPlanCode?: InputMaybe<Scalars['String']['input']>;
  externalPlanType?: InputMaybe<Scalars['String']['input']>;
  externalProvider?: InputMaybe<Scalars['String']['input']>;
  externalProviderId?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  genotype?: InputMaybe<Scalars['String']['input']>;
  height?: InputMaybe<Scalars['Int']['input']>;
  hmoId?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
  timezoneOffset?: InputMaybe<Scalars['String']['input']>;
  weight?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateProviderInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  hmoPlans?: InputMaybe<Array<HmoPlansInput>>;
  icon?: InputMaybe<Scalars['String']['input']>;
  iconAlt?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  monthlyConsultationLimit?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  planId?: InputMaybe<Scalars['String']['input']>;
  rareCase?: InputMaybe<Scalars['Boolean']['input']>;
  userTypeId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateReferralInput = {
  id: Scalars['String']['input'];
  status: Scalars['String']['input'];
};

export type UpdateRegenerateInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  hmoPlans?: InputMaybe<Array<HmoPlansInput>>;
  icon: Scalars['String']['input'];
  iconAlt: Scalars['String']['input'];
  id: Scalars['String']['input'];
  name: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
  planId?: InputMaybe<Scalars['String']['input']>;
  rareCase?: InputMaybe<Scalars['Boolean']['input']>;
  userTypeId: Scalars['String']['input'];
};

export type UpdateReminderInput = {
  date?: InputMaybe<Scalars['DateTime']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  interval?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateRoleInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  permissions?: InputMaybe<Array<Scalars['String']['input']>>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateSatisFactionInput = {
  doctorSatisfactionReason?: InputMaybe<Scalars['String']['input']>;
  doctorSatisfied?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['String']['input'];
  patientSatisfactionReason?: InputMaybe<Scalars['String']['input']>;
  patientSatisfied?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateSettingInput = {
  id: Scalars['String']['input'];
  key?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateSpecializationInput = {
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateTestInput = {
  approved?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  referralId: Scalars['String']['input'];
  tat?: InputMaybe<Scalars['String']['input']>;
  urgency?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserProviderInput = {
  dociId: Scalars['String']['input'];
  providerId: Scalars['ID']['input'];
};

export type UpdateUserTypeInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  icon?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UploadChatMessageInput = {
  consultationId: Scalars['ID']['input'];
  content: Scalars['String']['input'];
  receiver: Scalars['ID']['input'];
  receiverRole: Scalars['String']['input'];
  sender: Scalars['ID']['input'];
  senderRole: Scalars['String']['input'];
};

export type UploadEmployeesInput = {
  bucket: Scalars['String']['input'];
  businessId: Scalars['String']['input'];
  fileUrl: Scalars['String']['input'];
};

export type UploadEmployeesPayload = {
  __typename?: 'UploadEmployeesPayload';
  result?: Maybe<UploadEmployeesResult>;
};

export type UploadEmployeesResult = {
  __typename?: 'UploadEmployeesResult';
  bucket: Scalars['String']['output'];
  fileUrl: Scalars['String']['output'];
  totalInserted: Scalars['Float']['output'];
};

export type UploadEnrolleesInput = {
  bucket: Scalars['String']['input'];
  companyId?: InputMaybe<Scalars['String']['input']>;
  fileUrl: Scalars['String']['input'];
  override?: InputMaybe<Scalars['Boolean']['input']>;
  planId: Scalars['String']['input'];
  providerId: Scalars['String']['input'];
  replace?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UploadEnrolleesPayload = {
  __typename?: 'UploadEnrolleesPayload';
  errors: Array<ErrorPayload>;
  result: UploadEnrolleesResult;
};

export type UploadEnrolleesResult = {
  __typename?: 'UploadEnrolleesResult';
  bucket: Scalars['String']['output'];
  fileUrl: Scalars['String']['output'];
  totalInserted: Scalars['Float']['output'];
};

export type UploadLabTestsInput = {
  bucket: Scalars['String']['input'];
  fileUrl: Scalars['String']['input'];
};

export type UploadLabTestsPayload = {
  __typename?: 'UploadLabTestsPayload';
  result?: Maybe<UploadLabTestsResult>;
};

export type UploadLabTestsResult = {
  __typename?: 'UploadLabTestsResult';
  bucket?: Maybe<Scalars['String']['output']>;
  fileUrl?: Maybe<Scalars['String']['output']>;
  totalInserted?: Maybe<Scalars['Float']['output']>;
};

export type UploadPharmacyDrugsInput = {
  fileUrl: Scalars['String']['input'];
  partner: Scalars['String']['input'];
};

export type UploadPharmacyDrugsPayload = {
  __typename?: 'UploadPharmacyDrugsPayload';
  data: Array<PharmacyDrug>;
  errors: Array<ErrorPayload>;
};

export type UserAccountType = {
  __typename?: 'UserAccountType';
  _id: Scalars['ID']['output'];
  description?: Maybe<Scalars['String']['output']>;
  icon?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type UserLocationInput = {
  address: Scalars['String']['input'];
  city: Scalars['String']['input'];
  landmark: Scalars['String']['input'];
  lat: Scalars['Float']['input'];
  lga: Scalars['String']['input'];
  lng: Scalars['Float']['input'];
  phoneNumber: Scalars['String']['input'];
  state: Scalars['String']['input'];
};

export type UserPlanInput = {
  user: Scalars['String']['input'];
};

export type UserType = {
  __typename?: 'UserType';
  _id?: Maybe<Scalars['ID']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  icon?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  providerCount?: Maybe<Scalars['Int']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type UserTypeConnection = {
  __typename?: 'UserTypeConnection';
  pageInfo: PageInfo;
  userType: Array<UserType>;
};

export type UserTypePayload = {
  __typename?: 'UserTypePayload';
  errors: Array<ErrorPayload>;
  message?: Maybe<Scalars['String']['output']>;
  userType?: Maybe<UserType>;
};

export type ValidateEnrolleeInput = {
  hmoId: Scalars['String']['input'];
  providerId: Scalars['String']['input'];
};

export type Verification = {
  __typename?: 'Verification';
  _id?: Maybe<Scalars['ID']['output']>;
  alumni_association?: Maybe<Scalars['JSON']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  external_reference?: Maybe<Scalars['JSON']['output']>;
  license?: Maybe<Scalars['JSON']['output']>;
  licenseRenewalStatus?: Maybe<Scalars['String']['output']>;
  profileId?: Maybe<Scalars['JSON']['output']>;
  qualification?: Maybe<Scalars['JSON']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  reference?: Maybe<Scalars['JSON']['output']>;
  status?: Maybe<Scalars['Boolean']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  yearbook?: Maybe<Scalars['JSON']['output']>;
};

export type VerificationPayload = {
  __typename?: 'VerificationPayload';
  data: Verification;
  errors: Array<ErrorPayload>;
  message: Scalars['String']['output'];
};

export type VerificationsConnection = {
  __typename?: 'VerificationsConnection';
  pageInfo: PageInfo;
  verification: Array<Verification>;
};

export type VerifyEmailInput = {
  email: Scalars['String']['input'];
  otp: Scalars['String']['input'];
};

export type VerifyEmailPayload = {
  __typename?: 'VerifyEmailPayload';
  account: Account;
  message: Scalars['String']['output'];
};

export type VerifyHcpInput = {
  id: Scalars['String']['input'];
};

export type WellaHealthPharmaciesConnection = {
  __typename?: 'WellaHealthPharmaciesConnection';
  data: Array<WellaHealthPharmacy>;
};

export type WellaHealthPharmacy = {
  __typename?: 'WellaHealthPharmacy';
  address?: Maybe<Scalars['String']['output']>;
  area?: Maybe<Scalars['String']['output']>;
  lga?: Maybe<Scalars['String']['output']>;
  pharmacyCode?: Maybe<Scalars['String']['output']>;
  pharmacyName?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
};

export type WellaHealthPharmacyInput = {
  lga?: InputMaybe<Scalars['String']['input']>;
  state: Scalars['String']['input'];
};

export type WellaLgAsConnection = {
  __typename?: 'WellaLGAsConnection';
  data: Array<Scalars['String']['output']>;
};

export type Yearbook = {
  graduation_year: Scalars['String']['input'];
  image: Scalars['String']['input'];
};

export type ResendOtpMutationVariables = Exact<{
  email: Scalars['String']['input'];
}>;


export type ResendOtpMutation = { __typename?: 'Mutation', resendOTP: boolean };

export type VerifyEmailMutationVariables = Exact<{
  email: Scalars['String']['input'];
  otp: Scalars['String']['input'];
}>;


export type VerifyEmailMutation = { __typename?: 'Mutation', verifyEmail: { __typename?: 'VerifyEmailPayload', account: { __typename?: 'Account', _id?: string | null, email?: string | null, dociId?: string | null, access_token?: string | null, refresh_token?: string | null, isEmailVerified?: boolean | null } } };

export type SignOutDashboardMutationVariables = Exact<{ [key: string]: never; }>;


export type SignOutDashboardMutation = { __typename?: 'Mutation', logout: boolean };

export type GetIssuesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetIssuesQuery = { __typename?: 'Query', getIssues: { __typename?: 'IssuesConnection', issues: Array<{ __typename?: 'Symptoms', ID?: string | null, Name?: string | null }> } };

export type SendOtpMutationVariables = Exact<{
  email: Scalars['String']['input'];
}>;


export type SendOtpMutation = { __typename?: 'Mutation', sendOTP: boolean };

export type DoctorProfileQueryVariables = Exact<{
  profileId: Scalars['String']['input'];
}>;


export type DoctorProfileQuery = { __typename?: 'Query', doctorProfile: { __typename?: 'DoctorProfilePayload', profile?: { __typename?: 'DoctorProfile', _id?: string | null, firstName?: string | null, lastName?: string | null, gender?: string | null, phoneNumber?: string | null, createdAt?: any | null, updatedAt?: any | null, email?: string | null, fee?: number | null, hospital?: string | null, specialization?: string | null, dob?: any | null, cadre?: string | null, picture?: string | null, status?: string | null, dociId?: string | null, rating?: number | null, providerId?: { __typename?: 'ProviderType', _id?: string | null, name?: string | null } | null, accountDetails?: { __typename?: 'AccountDetails', accountName?: string | null, accountNumber?: string | null, bankName?: string | null } | null } | null } };

export type GetUserTypeProvidersQueryVariables = Exact<{
  hospitalUserTypeId?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetUserTypeProvidersQuery = { __typename?: 'Query', getUserTypeProviders: { __typename?: 'ProviderTypeConnection', provider?: Array<{ __typename?: 'ProviderPopulatedType', _id?: string | null, name: string, icon?: string | null, userCount?: number | null, doctorCount?: number | null, enrolleeCount?: number | null, partnerCount?: number | null, createdAt?: any | null, updatedAt?: any | null, userTypeId?: { __typename?: 'UserType', name?: string | null, icon?: string | null, createdAt?: any | null, updatedAt?: any | null } | null }> | null, pageInfo?: { __typename?: 'PageInfo', totalDocs?: number | null, limit?: number | null, offset?: number | null, hasPrevPage?: boolean | null, hasNextPage?: boolean | null, page?: number | null, totalPages?: number | null, pagingCounter?: number | null, prevPage?: number | null, nextPage?: number | null } | null } };

export type GetPartnerBySubdomainQueryVariables = Exact<{
  subdomain: Scalars['String']['input'];
}>;


export type GetPartnerBySubdomainQuery = { __typename?: 'Query', getPartnerBySubdomain: { __typename?: 'PartnerConfiguration', subdomain?: string | null, providerId?: string | null, widgetColor?: string | null, widgetLogo?: string | null, apiKey?: string | null, category?: string | null } };

export type GetRejectedDrugsOrderDataQueryVariables = Exact<{
  referralId: Scalars['String']['input'];
}>;


export type GetRejectedDrugsOrderDataQuery = { __typename?: 'Query', getRejectedDrugs: { __typename?: 'RejectedPayload', account?: { __typename?: 'Account', access_token?: string | null } | null, drugs?: Array<{ __typename?: 'Drug', _id?: string | null, drugForm?: string | null, drugName?: string | null, drugPrice?: number | null, quantity?: number | null }> | null } };

export type CheckChargeQueryVariables = Exact<{
  data: CheckChargeInput;
}>;


export type CheckChargeQuery = { __typename?: 'Query', checkCharge: { __typename?: 'CheckChargeConnection', chargeResponse: { __typename?: 'CheckChargeResponse', reference?: string | null, status?: string | null, amount?: string | null, channel?: string | null } } };

export type GetPharmaciesFromWellaHealthQueryVariables = Exact<{
  lga?: InputMaybe<Scalars['String']['input']>;
  state: Scalars['String']['input'];
}>;


export type GetPharmaciesFromWellaHealthQuery = { __typename?: 'Query', getPharmaciesFromWellaHealth: { __typename?: 'WellaHealthPharmaciesConnection', data: Array<{ __typename?: 'WellaHealthPharmacy', pharmacyCode?: string | null, pharmacyName?: string | null, state?: string | null, lga?: string | null, area?: string | null, address?: string | null }> } };

export type PrescriptionReferralQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type PrescriptionReferralQuery = { __typename?: 'Query', prescriptionReferral: { __typename?: 'PopulatedPrescriptionReferral', patient?: { __typename?: 'Profile', _id?: string | null, phoneNumber?: string | null, email?: string | null } | null, doctor?: { __typename?: 'UnPopulatedDoctorProfile', _id?: string | null } | null, consultation?: { __typename?: 'UnPopulatedConsultation', _id: string, pharmacyCode?: string | null, pharmacyName?: string | null, pharmacyAddress?: string | null } | null, drugsPartner?: { __typename?: 'Partner', _id: string } | null } };

export type GetPartnerConfigurationQueryVariables = Exact<{
  partnerId: Scalars['String']['input'];
}>;


export type GetPartnerConfigurationQuery = { __typename?: 'Query', getPartnerConfiguration: { __typename?: 'PartnerConfiguration', drugDeliveryFee?: number | null } };

export type GetScheduledConsultationsQueryVariables = Exact<{
  type?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  acceptedStatus?: InputMaybe<Scalars['String']['input']>;
  pendingStatus?: InputMaybe<Scalars['String']['input']>;
  patientId?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetScheduledConsultationsQuery = { __typename?: 'Query', acceptedConsultations: { __typename?: 'ConsultationConnection', data?: Array<{ __typename?: 'Consultation', _id: string, status?: string | null, type?: string | null, time?: any | null, fee?: number | null, contactMedium?: string | null, createdAt?: any | null, doctor?: { __typename?: 'Doctor', _id?: string | null, firstName?: string | null, lastName?: string | null, picture?: string | null, specialization?: string | null } | null }> | null }, pendingConsultations: { __typename?: 'ConsultationConnection', data?: Array<{ __typename?: 'Consultation', _id: string, status?: string | null, type?: string | null, time?: any | null, fee?: number | null, contactMedium?: string | null, createdAt?: any | null, doctor?: { __typename?: 'Doctor', _id?: string | null, firstName?: string | null, lastName?: string | null, picture?: string | null, specialization?: string | null } | null }> | null } };

export type GetAllUserConsultationsQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  patientId?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetAllUserConsultationsQuery = { __typename?: 'Query', getConsultations: { __typename?: 'ConsultationConnection', data?: Array<{ __typename?: 'Consultation', _id: string, status?: string | null, type?: string | null, time?: any | null, createdAt?: any | null, contactMedium?: string | null, doctor?: { __typename?: 'Doctor', _id?: string | null, firstName?: string | null, lastName?: string | null } | null }> | null, pageInfo?: { __typename?: 'PageInfo', totalDocs?: number | null, limit?: number | null, offset?: number | null, hasPrevPage?: boolean | null, hasNextPage?: boolean | null, page?: number | null, totalPages?: number | null, pagingCounter?: number | null, prevPage?: number | null, nextPage?: number | null } | null } };

export type GetOverviewStatsQueryVariables = Exact<{
  patientId: Scalars['String']['input'];
}>;


export type GetOverviewStatsQuery = { __typename?: 'Query', getConsultations: { __typename?: 'ConsultationConnection', pageInfo?: { __typename?: 'PageInfo', totalDocs?: number | null } | null }, getReferrals: { __typename?: 'ReferralConnection', pageInfo?: { __typename?: 'PageInfo', totalDocs?: number | null } | null }, getPrescriptions: { __typename?: 'PrescriptionsConnection', pageInfo?: { __typename?: 'PageInfo', totalDocs?: number | null } | null } };

export type GetAllPrescriptionsQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  patientId?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetAllPrescriptionsQuery = { __typename?: 'Query', getPrescriptions: { __typename?: 'PrescriptionsConnection', data?: Array<{ __typename?: 'Prescription', _id: string, consultation?: string | null, createdAt?: any | null, updatedAt?: any | null, doctor?: { __typename?: 'DoctorProfile', _id?: string | null, firstName?: string | null, lastName?: string | null } | null, drugs?: Array<{ __typename?: 'PrescriptionDrug', drugName?: string | null, dosageQuantity?: string | null, dosageUnit?: string | null, route?: string | null, instructions?: string | null, dosageFrequency?: { __typename?: 'DosageFreq', timing?: number | null, duration?: number | null } | null }> | null }> | null, pageInfo?: { __typename?: 'PageInfo', totalDocs?: number | null, limit?: number | null, offset?: number | null, hasPrevPage?: boolean | null, hasNextPage?: boolean | null, page?: number | null, totalPages?: number | null, pagingCounter?: number | null, prevPage?: number | null, nextPage?: number | null } | null } };

export type GetAllTestReferralsQueryVariables = Exact<{
  patientId?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetAllTestReferralsQuery = { __typename?: 'Query', getReferrals: { __typename?: 'ReferralConnection', referral: Array<{ __typename?: 'Referral', _id: string, type?: string | null, reason?: string | null, note?: string | null, createdAt?: any | null, updatedAt?: any | null, doctor?: { __typename?: 'DoctorProfile', _id?: string | null, firstName?: string | null, lastName?: string | null, picture?: string | null, specialization?: string | null, rating?: number | null } | null, tests?: Array<{ __typename?: 'DiagnosticLabTest', _id?: string | null, name?: string | null, note?: string | null, urgency?: string | null, partner?: string | null, paid?: boolean | null, createdAt?: any | null, updatedAt?: any | null }> | null }>, pageInfo?: { __typename?: 'PageInfo', totalDocs?: number | null, limit?: number | null, offset?: number | null, hasPrevPage?: boolean | null, hasNextPage?: boolean | null, page?: number | null, totalPages?: number | null, pagingCounter?: number | null, prevPage?: number | null, nextPage?: number | null } | null } };

export type FindProfilesQueryVariables = Exact<{
  healaId: Scalars['String']['input'];
}>;


export type FindProfilesQuery = { __typename?: 'Query', profiles: { __typename?: 'ProfileConnection', data: Array<{ __typename?: 'PopulatedProfile', _id?: string | null, firstName?: string | null, lastName?: string | null, height?: number | null, weight?: number | null, bloodGroup?: string | null, genotype?: string | null, gender?: string | null, phoneNumber?: string | null, plan?: string | null, status?: string | null, consultations?: number | null, createdAt?: any | null, image?: string | null, rating?: number | null, providerId?: { __typename?: 'ProviderType', _id?: string | null } | null, pastIllness?: Array<{ __typename?: 'IdPastIllnessType', id?: { __typename?: 'PastIllnessType', _id?: string | null, name?: string | null } | null }> | null, accountId?: { __typename?: 'Account', _id?: string | null, email?: string | null } | null }> } };


export const ResendOtpDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"resendOTP"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"email"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"resendOTP"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"email"},"value":{"kind":"Variable","name":{"kind":"Name","value":"email"}}}]}}]}]}}]} as unknown as DocumentNode<ResendOtpMutation, ResendOtpMutationVariables>;
export const VerifyEmailDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"verifyEmail"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"email"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"otp"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"verifyEmail"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"email"},"value":{"kind":"Variable","name":{"kind":"Name","value":"email"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"otp"},"value":{"kind":"Variable","name":{"kind":"Name","value":"otp"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"account"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"dociId"}},{"kind":"Field","name":{"kind":"Name","value":"access_token"}},{"kind":"Field","name":{"kind":"Name","value":"refresh_token"}},{"kind":"Field","name":{"kind":"Name","value":"isEmailVerified"}}]}}]}}]}}]} as unknown as DocumentNode<VerifyEmailMutation, VerifyEmailMutationVariables>;
export const SignOutDashboardDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"signOutDashboard"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"logout"}}]}}]} as unknown as DocumentNode<SignOutDashboardMutation, SignOutDashboardMutationVariables>;
export const GetIssuesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getIssues"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getIssues"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"issues"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ID"}},{"kind":"Field","name":{"kind":"Name","value":"Name"}}]}}]}}]}}]} as unknown as DocumentNode<GetIssuesQuery, GetIssuesQueryVariables>;
export const SendOtpDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"sendOTP"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"email"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"sendOTP"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"email"},"value":{"kind":"Variable","name":{"kind":"Name","value":"email"}}}]}}]}]}}]} as unknown as DocumentNode<SendOtpMutation, SendOtpMutationVariables>;
export const DoctorProfileDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"doctorProfile"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"profileId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"doctorProfile"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"profileId"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"profile"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"phoneNumber"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"fee"}},{"kind":"Field","name":{"kind":"Name","value":"hospital"}},{"kind":"Field","name":{"kind":"Name","value":"specialization"}},{"kind":"Field","name":{"kind":"Name","value":"dob"}},{"kind":"Field","name":{"kind":"Name","value":"cadre"}},{"kind":"Field","name":{"kind":"Name","value":"picture"}},{"kind":"Field","name":{"kind":"Name","value":"providerId"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"dociId"}},{"kind":"Field","name":{"kind":"Name","value":"rating"}},{"kind":"Field","name":{"kind":"Name","value":"accountDetails"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"accountName"}},{"kind":"Field","name":{"kind":"Name","value":"accountNumber"}},{"kind":"Field","name":{"kind":"Name","value":"bankName"}}]}}]}}]}}]}}]} as unknown as DocumentNode<DoctorProfileQuery, DoctorProfileQueryVariables>;
export const GetUserTypeProvidersDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getUserTypeProviders"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"hospitalUserTypeId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getUserTypeProviders"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"userTypeId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"hospitalUserTypeId"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"IntValue","value":"-1"}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"provider"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"icon"}},{"kind":"Field","name":{"kind":"Name","value":"userCount"}},{"kind":"Field","name":{"kind":"Name","value":"doctorCount"}},{"kind":"Field","name":{"kind":"Name","value":"enrolleeCount"}},{"kind":"Field","name":{"kind":"Name","value":"partnerCount"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}},{"kind":"Field","name":{"kind":"Name","value":"userTypeId"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"icon"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalDocs"}},{"kind":"Field","name":{"kind":"Name","value":"limit"}},{"kind":"Field","name":{"kind":"Name","value":"offset"}},{"kind":"Field","name":{"kind":"Name","value":"hasPrevPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"page"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"pagingCounter"}},{"kind":"Field","name":{"kind":"Name","value":"prevPage"}},{"kind":"Field","name":{"kind":"Name","value":"nextPage"}}]}}]}}]}}]} as unknown as DocumentNode<GetUserTypeProvidersQuery, GetUserTypeProvidersQueryVariables>;
export const GetPartnerBySubdomainDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getPartnerBySubdomain"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"subdomain"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPartnerBySubdomain"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"subdomain"},"value":{"kind":"Variable","name":{"kind":"Name","value":"subdomain"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"subdomain"}},{"kind":"Field","name":{"kind":"Name","value":"providerId"}},{"kind":"Field","name":{"kind":"Name","value":"widgetColor"}},{"kind":"Field","name":{"kind":"Name","value":"widgetLogo"}},{"kind":"Field","name":{"kind":"Name","value":"apiKey"}},{"kind":"Field","name":{"kind":"Name","value":"category"}}]}}]}}]} as unknown as DocumentNode<GetPartnerBySubdomainQuery, GetPartnerBySubdomainQueryVariables>;
export const GetRejectedDrugsOrderDataDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetRejectedDrugsOrderData"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"referralId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getRejectedDrugs"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"referralId"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"account"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"access_token"}}]}},{"kind":"Field","name":{"kind":"Name","value":"drugs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"drugForm"}},{"kind":"Field","name":{"kind":"Name","value":"drugName"}},{"kind":"Field","name":{"kind":"Name","value":"drugPrice"}},{"kind":"Field","name":{"kind":"Name","value":"quantity"}}]}}]}}]}}]} as unknown as DocumentNode<GetRejectedDrugsOrderDataQuery, GetRejectedDrugsOrderDataQueryVariables>;
export const CheckChargeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"CheckCharge"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"data"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CheckChargeInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"checkCharge"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"Variable","name":{"kind":"Name","value":"data"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"chargeResponse"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"amount"}},{"kind":"Field","name":{"kind":"Name","value":"channel"}}]}}]}}]}}]} as unknown as DocumentNode<CheckChargeQuery, CheckChargeQueryVariables>;
export const GetPharmaciesFromWellaHealthDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getPharmaciesFromWellaHealth"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"lga"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"state"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPharmaciesFromWellaHealth"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"state"},"value":{"kind":"Variable","name":{"kind":"Name","value":"state"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"lga"},"value":{"kind":"Variable","name":{"kind":"Name","value":"lga"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pharmacyCode"}},{"kind":"Field","name":{"kind":"Name","value":"pharmacyName"}},{"kind":"Field","name":{"kind":"Name","value":"state"}},{"kind":"Field","name":{"kind":"Name","value":"lga"}},{"kind":"Field","name":{"kind":"Name","value":"area"}},{"kind":"Field","name":{"kind":"Name","value":"address"}}]}}]}}]}}]} as unknown as DocumentNode<GetPharmaciesFromWellaHealthQuery, GetPharmaciesFromWellaHealthQueryVariables>;
export const PrescriptionReferralDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"PrescriptionReferral"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"prescriptionReferral"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"patient"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"phoneNumber"}},{"kind":"Field","name":{"kind":"Name","value":"email"}}]}},{"kind":"Field","name":{"kind":"Name","value":"doctor"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"consultation"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"pharmacyCode"}},{"kind":"Field","name":{"kind":"Name","value":"pharmacyName"}},{"kind":"Field","name":{"kind":"Name","value":"pharmacyAddress"}}]}},{"kind":"Field","name":{"kind":"Name","value":"drugsPartner"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}}]}}]}}]}}]} as unknown as DocumentNode<PrescriptionReferralQuery, PrescriptionReferralQueryVariables>;
export const GetPartnerConfigurationDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPartnerConfiguration"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"partnerId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPartnerConfiguration"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"partner"},"value":{"kind":"Variable","name":{"kind":"Name","value":"partnerId"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"drugDeliveryFee"}}]}}]}}]} as unknown as DocumentNode<GetPartnerConfigurationQuery, GetPartnerConfigurationQueryVariables>;
export const GetScheduledConsultationsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetScheduledConsultations"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"acceptedStatus"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pendingStatus"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","alias":{"kind":"Name","value":"acceptedConsultations"},"name":{"kind":"Name","value":"getConsultations"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"status"},"value":{"kind":"Variable","name":{"kind":"Name","value":"acceptedStatus"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"patient"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"orderBy"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"time"}},{"kind":"Field","name":{"kind":"Name","value":"fee"}},{"kind":"Field","name":{"kind":"Name","value":"contactMedium"}},{"kind":"Field","name":{"kind":"Name","value":"doctor"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}},{"kind":"Field","name":{"kind":"Name","value":"picture"}},{"kind":"Field","name":{"kind":"Name","value":"specialization"}}]}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}}]}}]}},{"kind":"Field","alias":{"kind":"Name","value":"pendingConsultations"},"name":{"kind":"Name","value":"getConsultations"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"status"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pendingStatus"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"patient"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"orderBy"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"time"}},{"kind":"Field","name":{"kind":"Name","value":"fee"}},{"kind":"Field","name":{"kind":"Name","value":"contactMedium"}},{"kind":"Field","name":{"kind":"Name","value":"doctor"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}},{"kind":"Field","name":{"kind":"Name","value":"picture"}},{"kind":"Field","name":{"kind":"Name","value":"specialization"}}]}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}}]}}]}}]}}]} as unknown as DocumentNode<GetScheduledConsultationsQuery, GetScheduledConsultationsQueryVariables>;
export const GetAllUserConsultationsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getAllUserConsultations"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getConsultations"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"patient"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"orderBy"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"time"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"contactMedium"}},{"kind":"Field","name":{"kind":"Name","value":"doctor"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalDocs"}},{"kind":"Field","name":{"kind":"Name","value":"limit"}},{"kind":"Field","name":{"kind":"Name","value":"offset"}},{"kind":"Field","name":{"kind":"Name","value":"hasPrevPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"page"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"pagingCounter"}},{"kind":"Field","name":{"kind":"Name","value":"prevPage"}},{"kind":"Field","name":{"kind":"Name","value":"nextPage"}}]}}]}}]}}]} as unknown as DocumentNode<GetAllUserConsultationsQuery, GetAllUserConsultationsQueryVariables>;
export const GetOverviewStatsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getOverviewStats"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getConsultations"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"patient"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalDocs"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"getReferrals"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"patient"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalDocs"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"getPrescriptions"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"patient"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalDocs"}}]}}]}}]}}]} as unknown as DocumentNode<GetOverviewStatsQuery, GetOverviewStatsQueryVariables>;
export const GetAllPrescriptionsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getAllPrescriptions"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPrescriptions"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"patient"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"orderBy"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"doctor"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}}]}},{"kind":"Field","name":{"kind":"Name","value":"consultation"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}},{"kind":"Field","name":{"kind":"Name","value":"drugs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"drugName"}},{"kind":"Field","name":{"kind":"Name","value":"dosageQuantity"}},{"kind":"Field","name":{"kind":"Name","value":"dosageUnit"}},{"kind":"Field","name":{"kind":"Name","value":"route"}},{"kind":"Field","name":{"kind":"Name","value":"instructions"}},{"kind":"Field","name":{"kind":"Name","value":"dosageFrequency"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"timing"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalDocs"}},{"kind":"Field","name":{"kind":"Name","value":"limit"}},{"kind":"Field","name":{"kind":"Name","value":"offset"}},{"kind":"Field","name":{"kind":"Name","value":"hasPrevPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"page"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"pagingCounter"}},{"kind":"Field","name":{"kind":"Name","value":"prevPage"}},{"kind":"Field","name":{"kind":"Name","value":"nextPage"}}]}}]}}]}}]} as unknown as DocumentNode<GetAllPrescriptionsQuery, GetAllPrescriptionsQueryVariables>;
export const GetAllTestReferralsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getAllTestReferrals"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getReferrals"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filterBy"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"patient"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patientId"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"orderBy"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderBy"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"referral"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"doctor"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}},{"kind":"Field","name":{"kind":"Name","value":"picture"}},{"kind":"Field","name":{"kind":"Name","value":"specialization"}},{"kind":"Field","name":{"kind":"Name","value":"rating"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tests"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"note"}},{"kind":"Field","name":{"kind":"Name","value":"urgency"}},{"kind":"Field","name":{"kind":"Name","value":"partner"}},{"kind":"Field","name":{"kind":"Name","value":"paid"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"reason"}},{"kind":"Field","name":{"kind":"Name","value":"note"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalDocs"}},{"kind":"Field","name":{"kind":"Name","value":"limit"}},{"kind":"Field","name":{"kind":"Name","value":"offset"}},{"kind":"Field","name":{"kind":"Name","value":"hasPrevPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"page"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"pagingCounter"}},{"kind":"Field","name":{"kind":"Name","value":"prevPage"}},{"kind":"Field","name":{"kind":"Name","value":"nextPage"}}]}}]}}]}}]} as unknown as DocumentNode<GetAllTestReferralsQuery, GetAllTestReferralsQueryVariables>;
export const FindProfilesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"findProfiles"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"healaId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"profiles"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"search"},"value":{"kind":"Variable","name":{"kind":"Name","value":"healaId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"weight"}},{"kind":"Field","name":{"kind":"Name","value":"bloodGroup"}},{"kind":"Field","name":{"kind":"Name","value":"genotype"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"phoneNumber"}},{"kind":"Field","name":{"kind":"Name","value":"providerId"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"plan"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"consultations"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"image"}},{"kind":"Field","name":{"kind":"Name","value":"rating"}},{"kind":"Field","name":{"kind":"Name","value":"pastIllness"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"accountId"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"email"}}]}}]}}]}}]}}]} as unknown as DocumentNode<FindProfilesQuery, FindProfilesQueryVariables>;