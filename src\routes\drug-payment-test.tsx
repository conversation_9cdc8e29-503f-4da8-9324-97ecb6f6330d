import { Head } from "@/components/head";
import { Button } from "@/components/ui/button";
import HealaLogoIcon from "@/components/icons/heala-logo-icon";
import { createFileRoute, useNavigate } from "@tanstack/react-router";

export const Route = createFileRoute("/drug-payment-test")({
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();

  const handleTestPayment = () => {
    // Generate a mock payment ID
    const testReferralId = '6830797bd213bb4408a96bb6';

    // Navigate to the drug payment page with the mock payment ID
    navigate({
      to: "/drug-payment/$referralId",
      params: { referralId: testReferralId }
    });
  };

  return (
    <>
      <Head title="Test Drug Payment" />
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 p-4">
        <div className="mb-6">
          <HealaLogoIcon className="w-16 h-16" />
        </div>

        <div className="bg-white rounded-lg shadow-md w-full max-w-md p-8 text-center">
          <h1 className="text-2xl font-bold mb-6">Drug Payment Test</h1>

          <p className="text-gray-600 mb-6">
            This page is for testing the drug payment feature. Click the button below to simulate receiving a payment link.
          </p>

          <Button onClick={handleTestPayment} className="w-full">
            Test Drug Payment
          </Button>
        </div>
      </div>
    </>
  );
}
