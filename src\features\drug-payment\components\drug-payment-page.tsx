import { useEffect, useState } from "react";
import { defaultDeliveryOptions, useDrugPaymentStore } from "../hooks/use-drug-payment-store";
import { DrugList } from "./drug-list";
import { DeliveryOptions } from "./delivery-options";
import { PaymentSummary } from "./payment-summary";
import { Button } from "@/components/ui/button";
import { HealaLoader } from "@/components/heala-loader";
import HealaLogoIcon from "@/components/icons/heala-logo-icon";
import { X } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { useQuery as useApolloQuery, useLazyQuery } from "@apollo/client";
import { getRejectedDrugsQuery, prescriptionReferralQuery, getPartnerConfigurationQuery } from "@/graphql/queries";
import { DrugPaymentData } from "../types";

interface DrugPaymentPageProps {
  referralId: string;
}

export function DrugPaymentPage({ referralId }: DrugPaymentPageProps) {
  const {
    paymentData,
    isLoading: storeLoading,
    error: storeError,
    selectedDeliveryOption,
    deliveryAddress,
    setPaymentData,
    setLoading,
    setError,
    updateDeliveryOptions
  } = useDrugPaymentStore();

  // State to track the sequential loading process
  const [sequentialLoading, setSequentialLoading] = useState({
    rejectedDrugs: false,
    prescriptionReferral: false,
    partnerConfiguration: false
  });

  const navigate = useNavigate();

  // Lazy queries for sequential data fetching
  const [fetchPrescriptionReferral] = useLazyQuery(prescriptionReferralQuery, {
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      setSequentialLoading(prev => ({ ...prev, prescriptionReferral: false }));

      if (data.prescriptionReferral?.drugsPartner?._id) {
        // Execute the third query to get partner configuration
        setSequentialLoading(prev => ({ ...prev, partnerConfiguration: true }));
        fetchPartnerConfiguration({
          variables: { partnerId: data.prescriptionReferral.drugsPartner._id }
        });
      } else {
        console.error("No drugs partner ID found in prescription referral data");
        // Use default delivery fee if no partner ID is found
        updateDeliveryOptions(3000.00);
      }
    },
    onError: (error) => {
      setSequentialLoading(prev => ({ ...prev, prescriptionReferral: false }));
      console.error("Error fetching prescription referral:", error);
      // Use default delivery fee on error
      updateDeliveryOptions(3000.00);
    }
  });

  const [fetchPartnerConfiguration] = useLazyQuery(getPartnerConfigurationQuery, {
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      setSequentialLoading(prev => ({ ...prev, partnerConfiguration: false }));

      const deliveryFee = data.getPartnerConfiguration?.drugDeliveryFee;
      if (deliveryFee !== null && deliveryFee !== undefined) {
        updateDeliveryOptions(deliveryFee);
      } else {
        const partnerId = data.getPartnerConfiguration ? "unknown" : "not found";
        console.error(`Error: No delivery fee set for partner with id ${partnerId}`);
        // Use default value or 'Free' if 0
        updateDeliveryOptions(0);
      }
    },
    onError: (error) => {
      setSequentialLoading(prev => ({ ...prev, partnerConfiguration: false }));
      console.error("Error fetching partner configuration:", error);
      // Use default delivery fee on error
      updateDeliveryOptions(3000.00);
    }
  });

  // Use Apollo's useQuery hook to fetch rejected drugs data
  const { loading: queryLoading, error: queryError } = useApolloQuery(getRejectedDrugsQuery, {
    variables: { referralId },
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      setSequentialLoading(prev => ({ ...prev, rejectedDrugs: false }));

      if (!data.getRejectedDrugs.drugs) {
        setError("No drug data found");
        return;
      }

      // Step 1: Extract and store access token
      const accessToken = data.getRejectedDrugs.account?.access_token;
      if (accessToken) {
        sessionStorage.setItem('token', accessToken);
      } else {
        console.warn("No access token found in rejected drugs response");
      }

      // Transform the data to match our DrugPaymentData structure
      const transformedData: DrugPaymentData = {
        id: referralId,
        patientName: "Patient", // This would come from the API in a real implementation
        patientEmail: "<EMAIL>", // This would come from the API in a real implementation
        drugs: data.getRejectedDrugs.drugs.map(drug => ({
          _id: drug._id || "",
          drugName: drug.drugName || "",
          drugForm: drug.drugForm || "",
          quantity: drug.quantity || 0,
          drugPrice: drug.drugPrice || 0
        })),
        deliveryOptions: defaultDeliveryOptions,
      };

      setPaymentData(transformedData);

      // Step 2: Execute prescription referral query
      setSequentialLoading(prev => ({ ...prev, prescriptionReferral: true }));
      fetchPrescriptionReferral({
        variables: { id: referralId }
      });
    },
    onError: (error) => {
      setSequentialLoading(prev => ({ ...prev, rejectedDrugs: false }));
      console.error("Error fetching rejected drugs:", error);
      setError(error.message || "Failed to fetch payment data. Please try again.");
    }
  });

  // Update loading state from query and sequential loading
  useEffect(() => {
    const isSequentialLoading = sequentialLoading.rejectedDrugs ||
      sequentialLoading.prescriptionReferral ||
      sequentialLoading.partnerConfiguration;
    setLoading(queryLoading || isSequentialLoading);
  }, [queryLoading, sequentialLoading, setLoading]);

  // Set initial loading state for the first query
  useEffect(() => {
    if (queryLoading) {
      setSequentialLoading(prev => ({ ...prev, rejectedDrugs: true }));
    }
  }, [queryLoading]);

  // Combine errors from store and query
  const error = storeError || (queryError ? queryError.message : null);
  const isLoading = storeLoading || queryLoading ||
    sequentialLoading.prescriptionReferral ||
    sequentialLoading.partnerConfiguration;

  const handlePlaceOrder = async () => {
    // Check if delivery is selected but no address is provided
    if (selectedDeliveryOption === "delivery" && !deliveryAddress) {
      alert("Please add a delivery address before proceeding.");
      return;
    }

    // In a real implementation, this would make an API call to process the payment
    // and redirect to a payment gateway

    // For demo purposes, we'll simulate a successful payment and redirect to success page
    // In production, this would redirect to a payment gateway first
    const reference = `REF-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
    navigate({
      to: "/drug-payment-success/$referralId",
      params: { referralId },
      search: { reference }
    });
  };

  const handleClose = () => {
    // Navigate back or to a specific page
    navigate({ to: "/" });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <HealaLoader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen p-4">
        <div className="text-red-500 text-xl mb-4">Error: {error}</div>
        <Button onClick={() => window.location.reload()}>Try Again</Button>
      </div>
    );
  }

  if (!paymentData) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-gray-500">No payment data found</div>
      </div>
    );
  }

  const selectedOption = paymentData.deliveryOptions.find(
    option => option.id === selectedDeliveryOption
  );

  const deliveryPrice = selectedOption?.price || 0;
  const subtotal = paymentData.drugs.reduce((sum, drug) => sum + (drug.drugPrice * drug.quantity), 0);
  const total = subtotal + deliveryPrice;

  return (
    <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 p-4">
      {/* Heala Logo */}
      <div className="mb-6">
        <HealaLogoIcon className="w-16 h-16" />
      </div>

      <div className="bg-white rounded-lg shadow-md w-full max-w-2xl relative">
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          aria-label="Close"
        >
          <X size={20} />
        </button>

        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">Checkout</h1>

          <div className="border-t border-gray-200 my-4"></div>

          <DrugList drugs={paymentData.drugs} />

          <div className="border-t border-gray-200 my-4"></div>

          <DeliveryOptions
            options={paymentData.deliveryOptions}
            selectedOptionId={selectedDeliveryOption || ''}
          />

          <div className="border-t border-gray-200 my-4"></div>

          <PaymentSummary
            subtotal={subtotal}
            deliveryPrice={deliveryPrice}
            total={total}
          />

          <Button
            className="w-full mt-6 py-6"
            onClick={handlePlaceOrder}
          >
            Place Order
          </Button>
        </div>
      </div>
    </div>
  );
}

